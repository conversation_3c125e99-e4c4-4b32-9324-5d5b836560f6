<script setup lang="ts">
import { inject, watch, computed } from 'vue';
import { ElButton } from 'element-plus';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import type { VxeTableGridOptions } from '#/adapter/vxe-table';

// 设备数据类型
interface DeviceData {
  id: string;
  name: string;
  type: 'transformer' | 'switchgear' | 'meter';
  address: string;
  status: 'online' | 'offline' | 'fault';
  location: string;
  lastUpdate: string;
  values: {
    voltage?: number;
    current?: number;
    power?: number;
    temperature?: number;
    [key: string]: any;
  };
}

// 从父组件注入设备数据
const devices = inject<DeviceData[]>('devices', []);
const filteredDevices = inject<DeviceData[]>('filteredDevices', []);

// 表格列配置
const useColumns = (): VxeTableGridOptions<DeviceData>['columns'] => [
  {
    type: 'seq',
    title: '序号',
    width: 60,
    align: 'center',
    headerAlign: 'center',
  },
  {
    field: 'name',
    title: '设备名称',
    minWidth: 120,
    align: 'left',
    headerAlign: 'center',
  },
  {
    field: 'type',
    title: '设备类型',
    width: 100,
    align: 'center',
    headerAlign: 'center',
    formatter: ({ cellValue }) => {
      const typeMap = {
        transformer: '变压器',
        switchgear: '开关柜',
        meter: '电表',
      };
      return typeMap[cellValue as keyof typeof typeMap] || cellValue;
    },
  },
  {
    field: 'address',
    title: '设备地址',
    width: 120,
    align: 'center',
    headerAlign: 'center',
  },
  {
    field: 'location',
    title: '位置',
    width: 100,
    align: 'center',
    headerAlign: 'center',
  },
  {
    field: 'status',
    title: '状态',
    width: 80,
    align: 'center',
    headerAlign: 'center',
    slots: { default: 'status' },
  },
  {
    field: 'values.voltage',
    title: '电压(V)',
    width: 100,
    align: 'center',
    headerAlign: 'center',
    formatter: ({ cellValue }) => cellValue ? `${cellValue}V` : '-',
  },
  {
    field: 'values.current',
    title: '电流(A)',
    width: 100,
    align: 'center',
    headerAlign: 'center',
    formatter: ({ cellValue }) => cellValue ? `${cellValue}A` : '-',
  },
  {
    field: 'values.power',
    title: '功率(kW)',
    width: 100,
    align: 'center',
    headerAlign: 'center',
    formatter: ({ cellValue }) => cellValue ? `${cellValue}kW` : '-',
  },
  {
    field: 'values.temperature',
    title: '温度(°C)',
    width: 100,
    align: 'center',
    headerAlign: 'center',
    formatter: ({ cellValue }) => cellValue ? `${cellValue}°C` : '-',
  },
  {
    field: 'lastUpdate',
    title: '最后更新',
    width: 160,
    align: 'center',
    headerAlign: 'center',
    formatter: ({ cellValue }) => {
      const date = new Date(cellValue);
      return date.toLocaleString();
    },
  },
  {
    title: '操作',
    width: 120,
    align: 'center',
    headerAlign: 'center',
    slots: { default: 'action' },
  },
];

// 创建表格配置
const createGridOptions = (): VxeTableGridOptions<DeviceData> => ({
  columns: useColumns(),
  height: '99%',
  keepSource: true,
  showFooter: false,
  autoResize: true,
  columnConfig: {
    resizable: true,
    useKey: true,
    isCurrent: false,
  },
  fit: true,
  autoWidth: true,
  border: true,
  stripe: true,
  showOverflow: true,
  pagerConfig: {
    enabled: true,
    currentPage: 1,
    pageSize: 20,
    pageSizes: [10, 20, 50, 100],
    showTotal: true,
    showJumper: true,
    showSizes: true,
  },
  rowConfig: {
    keyField: 'id',
  },
});

// 表格事件处理
const gridEvents = {
  currentRowChange: (params: any) => {
    console.log('当前行变化:', params);
  },
  cellClick: (params: any) => {
    console.log('单元格点击:', params);
  },
};

// 创建VXE表格 - 纯展示表格，无搜索表单
const [Grid, gridApi] = useVbenVxeGrid({
  gridEvents,
  showSearchForm: false, // 不显示搜索表单
  gridOptions: {
    ...createGridOptions(),
    proxyConfig: {
      autoLoad: false, // 不自动加载，使用注入的数据
    },
  },
});

// 监听过滤后的设备数据变化，直接更新表格数据
watch(
  () => filteredDevices.value,
  (newDevices) => {
    gridApi.setGridOptions({
      data: newDevices,
      pagerConfig: {
        enabled: true,
        currentPage: 1,
        pageSize: 20,
        total: newDevices.length,
        pageSizes: [10, 20, 50, 100],
        showTotal: true,
        showJumper: true,
        showSizes: true,
      },
    });
  },
  { immediate: true, deep: true }
);

// 操作按钮事件
const handleDetail = (device: DeviceData) => {
  console.log('查看详情:', device);
};

const handleControl = (device: DeviceData) => {
  console.log('设备控制:', device);
};
</script>

<template>
  <div class="h-full">
    <Grid class="h-full">
      <!-- 状态列自定义渲染 -->
      <template #status="{ row }">
        <div class="flex items-center justify-center gap-1">
          <div
            :class="{
              'bg-green-500': row.status === 'online',
              'bg-gray-400': row.status === 'offline',
              'bg-red-500': row.status === 'fault'
            }"
            class="h-2 w-2 rounded-full"
          ></div>
          <span
            :class="{
              'text-green-600': row.status === 'online',
              'text-gray-500': row.status === 'offline',
              'text-red-600': row.status === 'fault'
            }"
            class="text-xs font-medium"
          >
            {{ row.status === 'online' ? '在线' : row.status === 'offline' ? '离线' : '故障' }}
          </span>
        </div>
      </template>

      <!-- 操作列自定义渲染 -->
      <template #action="{ row }">
        <div class="flex items-center justify-center gap-1">
          <ElButton
            type="primary"
            size="small"
            @click="handleDetail(row)"
          >
            详情
          </ElButton>
          <ElButton
            type="info"
            size="small"
            @click="handleControl(row)"
          >
            控制
          </ElButton>
        </div>
      </template>
    </Grid>
  </div>
</template>
