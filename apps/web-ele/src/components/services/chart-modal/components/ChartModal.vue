<script lang="ts" setup>
import type { EchartsUIType } from '@vben/plugins/echarts';

import type { ChartModalEmits, ChartModalProps } from '../types';

import { computed, nextTick, ref, watch } from 'vue';

import { globalShareState, useVbenModal } from '@vben/common-ui';
import { EchartsUI, useEcharts } from '@vben/plugins/echarts';

import { IconifyIcon } from '@vben-core/icons';

import dayjs from 'dayjs';
import { ElButton, ElDatePicker, ElOption, ElSelect } from 'element-plus';

import { useVbenVxeGrid } from '#/adapter/vxe-table';

import {
  chartConfigs,
  generateLoadRateData,
  generateTimeData,
  loadRateOptions,
} from '../data';

const props = withDefaults(defineProps<ChartModalProps>(), {
  visible: false,
  title: '负载率曲线',
  chartType: 'loadRate',
});

const emit = defineEmits<ChartModalEmits>();

// 获取adapter中注册的组件
const { PrimaryButton } = globalShareState.getComponents();

// Modal配置
const [Modal, modalApi] = useVbenModal({
  onCancel() {
    emit('cancel');
    modalApi.close();
  },
  onConfirm() {
    emit('confirm');
    modalApi.close();
  },
  onOpenChange(isOpen: boolean) {
    // 当弹窗关闭时（点击遮罩、ESC等），同步父组件状态
    if (isOpen) {
      console.log('ChartModal: Modal opening, will reinit chart...');
    } else {
      console.log('ChartModal: Modal closing, disposing chart...');
      // 主动清理图表实例
      disposeChart();
      emit('cancel');
    }
  },
});

// 图表相关
const chartRef = ref<EchartsUIType>();
const { renderEcharts, disposeChart, reinitChart } = useEcharts(chartRef);

// 状态管理 - 使用 dayjs 作为内部状态
const currentDate = ref(dayjs());
const selectedMetric = ref('loadRate');
const showConnection = ref(true);
const showData = ref(false);
const showTable = ref(false); // 新增：控制显示图表还是表格

// 用于 v-model 的纯计算属性（无副作用）
const datePickerValue = computed({
  get: () => currentDate.value.format('YYYY-MM-DD'),
  set: (value: string) => {
    console.log('datePickerValue setter called with:', value);
    // 只更新状态，不执行副作用
    currentDate.value = dayjs(value);
  },
});

// 计算属性
const chartConfig = computed(
  () => chartConfigs[selectedMetric.value] || chartConfigs.loadRate,
);
const displayTitle = computed(() => {
  const config = chartConfig.value;
  return `${currentDate.value.format('YYYY-MM-DD')} 10kV Trans. ${config.title}`;
});

// 缓存静态数据 - 时间轴数据不会变化
const timeData = computed(() => generateTimeData());

// 表格数据
const tableData = computed(() => {
  const times = timeData.value;
  const todayData = generateLoadRateData();
  const yesterdayData = generateLoadRateData();

  return times.map((time, index) => ({
    time,
    today: todayData[index]?.value?.toFixed(2) || '--',
    yesterday: yesterdayData[index]?.value?.toFixed(2) || '--',
  }));
});

// 表格列配置
const tableColumns = [
  {
    field: 'time',
    title: '时间',
    minWidth: 100,
    align: 'center',
  },
  {
    field: 'today',
    title: '当日(%)',
    minWidth: 100,
    align: 'center',
  },
  {
    field: 'yesterday',
    title: '上日(%)',
    minWidth: 100,
    align: 'center',
  },
];

// 创建表格实例
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: {
    columns: tableColumns,
    data: [],
    height: '100%', // 使用百分比高度，由容器控制
    maxHeight: 450, // 设置最大高度
    keepSource: true,
    autoResize: true,
    columnConfig: {
      resizable: true,
      useKey: true,
    },
    border: true,
    stripe: true,
    pagerConfig: {
      enabled: false,
    },
    showOverflow: true,
    scrollY: {
      enabled: true, // 启用垂直滚动
    },
  },
});

// 监听 currentDate 变化，使用增量更新
watch(
  currentDate,
  (newDate, oldDate) => {
    // 只有当日期真正改变时才重新渲染图表
    if (newDate && oldDate && !newDate.isSame(oldDate, 'day')) {
      console.log('currentDate changed, performing incremental update...');
      renderChart(true); // 传入 true 表示增量更新
    }
  },
  { deep: false },
);

// 监听工具栏状态变化，使用增量更新（排除 showTable，因为它不影响图表渲染）
watch(
  [showConnection, showData, selectedMetric],
  () => {
    console.log('Toolbar state changed, performing incremental update...');
    // 只有在显示图表时才重新渲染
    if (!showTable.value) {
      renderChart(true); // 传入 true 表示增量更新
    }
  },
  { deep: false },
);

// 监听表格数据变化，更新表格
watch(
  [tableData, currentDate],
  () => {
    if (showTable.value && gridApi) {
      // 重新生成表格数据
      const times = timeData.value;
      const todayData = generateLoadRateData();
      const yesterdayData = generateLoadRateData();

      const newData = times.map((time, index) => ({
        time,
        today: todayData[index]?.value?.toFixed(2) || '--',
        yesterday: yesterdayData[index]?.value?.toFixed(2) || '--',
      }));

      gridApi.setGridOptions({ data: newData });
    }
  },
  { deep: false },
);

// 监听visible变化
watch(
  () => props.visible,
  (newVal) => {
    console.log('ChartModal: props.visible changed to:', newVal);
    if (newVal) {
      console.log('ChartModal: opening modal...');
      modalApi.open();
      nextTick(() => {
        console.log('ChartModal: nextTick - reinitializing chart...');
        // 重新初始化图表实例，确保干净的状态
        reinitChart();
        setTimeout(() => {
          console.log(
            'ChartModal: timeout - about to render chart with full rebuild',
          );
          renderChart(false); // 传入 false 表示完全重建
        }, 400); // 增加延迟时间，确保DOM完全准备就绪
      });
    } else {
      console.log('ChartModal: closing modal...');
      modalApi.close();
    }
  },
);

// 监听 showTable 变化，当切换回图表时重建图表
watch(showTable, (newVal, oldVal) => {
  console.log('ChartModal: showTable changed from', oldVal, 'to', newVal);
  // 当从表格切换回图表时（newVal: false, oldVal: true）
  if (!newVal && oldVal && props.visible) {
    console.log('ChartModal: switching back to chart, reinitializing...');
    nextTick(() => {
      // 重新初始化图表实例
      reinitChart();
      setTimeout(() => {
        console.log('ChartModal: rendering chart after table switch');
        renderChart(false); // 完全重建
      }, 100); // 较短的延迟，因为DOM已经准备好了
    });
  }
});

// 渲染图表
const renderChart = async (incrementalUpdate = false) => {
  console.log(
    'ChartModal: renderChart called, incrementalUpdate:',
    incrementalUpdate,
  );
  console.log('ChartModal: chartRef.value:', chartRef.value);

  if (!chartRef.value) {
    console.warn('ChartModal: chartRef is null, cannot render chart');
    return;
  }

  try {
    // 生成今日和昨日的对比数据
    const todayData = generateLoadRateData();
    const yesterdayData = generateLoadRateData(); // 模拟昨日数据
    const config = chartConfig.value;

    let option;

    if (incrementalUpdate) {
      // 增量更新：只传递变化的部分
      option = {
        title: {
          text: displayTitle.value,
        },
        series: [
          {
            name: '今日',
            data: todayData.map((item) => item.value),
            areaStyle: showConnection.value
              ? {
                  color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [
                      { offset: 0, color: `${config.color}40` },
                      { offset: 1, color: `${config.color}10` },
                    ],
                  },
                }
              : undefined,
            symbol: showData.value ? 'circle' : 'none',
          },
          {
            name: '昨日',
            data: yesterdayData.map((item) => item.value),
            areaStyle: showConnection.value
              ? {
                  color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [
                      { offset: 0, color: '#ff644440' },
                      { offset: 1, color: '#ff644410' },
                    ],
                  },
                }
              : undefined,
            symbol: showData.value ? 'circle' : 'none',
          },
        ],
      };
    } else {
      // 完整配置：包含所有设置
      option = {
        title: {
          text: displayTitle.value,
          left: 'center',
          top: 0,
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold',
          },
        },
        grid: {
          left: '8%',
          right: '8%',
          top: '15%',
          bottom: '15%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          data: timeData.value,
          boundaryGap: false,
          axisLabel: {
            fontSize: 11,
            interval: 1,
          },
          axisTick: {
            show: false,
          },
        },
        yAxis: {
          type: 'value',
          min: config.yAxisMin,
          max: config.yAxisMax,
          axisLabel: {
            fontSize: 11,
            formatter: `{value}${config.unit}`,
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            lineStyle: {
              type: 'dashed',
            },
          },
        },
        legend: {
          data: ['今日', '昨日'],
          top: 25,
          left: 'center',
          textStyle: {
            fontSize: 12,
          },
        },
        series: [
          {
            name: '今日',
            type: 'line',
            data: todayData.map((item) => item.value),
            smooth: true,
            lineStyle: {
              color: config.color,
              width: 2,
            },
            itemStyle: {
              color: config.color,
            },
            areaStyle: showConnection.value
              ? {
                  color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [
                      { offset: 0, color: `${config.color}40` },
                      { offset: 1, color: `${config.color}10` },
                    ],
                  },
                }
              : undefined,
            symbol: showData.value ? 'circle' : 'none',
            symbolSize: 4,
          },
          {
            name: '昨日',
            type: 'line',
            data: yesterdayData.map((item) => item.value),
            smooth: true,
            lineStyle: {
              color: '#ff6444',
              width: 2,
            },
            itemStyle: {
              color: '#ff6444',
            },
            areaStyle: showConnection.value
              ? {
                  color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [
                      { offset: 0, color: '#ff644440' },
                      { offset: 1, color: '#ff644410' },
                    ],
                  },
                }
              : undefined,
            symbol: showData.value ? 'circle' : 'none',
            symbolSize: 4,
            markLine: {
              data: [
                {
                  yAxis: 70,
                  lineStyle: { color: '#ff4444', type: 'solid', width: 2 },
                  label: { formatter: 'Max:63.88', position: 'end' },
                },
                {
                  yAxis: 40,
                  lineStyle: { color: '#ff4444', type: 'solid', width: 2 },
                  label: { formatter: 'Min:12.76', position: 'end' },
                },
              ],
            },
          },
        ],
        tooltip: {
          trigger: 'axis',
          formatter: (params: any) => {
            const data = params[0];
            return `${data.axisValue}<br/>${data.marker}${config.title}: ${data.value}${config.unit}`;
          },
        },
        dataZoom: [
          {
            type: 'slider',
            show: true,
            xAxisIndex: [0],
            start: 0,
            end: 100,
            bottom: 10,
            height: 20,
            handleStyle: {
              color: config.color,
            },
            textStyle: {
              color: '#666',
            },
          },
          {
            type: 'slider',
            show: true,
            yAxisIndex: [0],
            start: 0,
            end: 100,
            right: 10,
            width: 20,
            handleStyle: {
              color: config.color,
            },
            textStyle: {
              color: '#666',
            },
          },
        ],
      };
    }

    console.log(
      'ChartModal: calling renderEcharts with option:',
      option,
      'incrementalUpdate:',
      incrementalUpdate,
    );
    const chartInstance = await renderEcharts(
      option,
      !incrementalUpdate,
      0,
      incrementalUpdate,
    );
    if (chartInstance) {
      console.log('ChartModal: renderEcharts completed successfully');
    } else {
      console.warn('ChartModal: renderEcharts returned null');
    }
  } catch (error) {
    console.error('ChartModal: Error rendering chart:', error);
    // 如果渲染失败，尝试重新初始化
    setTimeout(() => {
      console.log('ChartModal: Retrying chart render after error...');
      reinitChart();
      renderChart(false); // 错误重试时使用完全重建
    }, 500);
  }
};

// 事件处理 - 使用 dayjs API，移除重复的 renderChart 调用
const handlePreviousDay = () => {
  currentDate.value = currentDate.value.subtract(1, 'day');
  // 移除 renderChart()，由 watch 统一处理
};

const handleNextDay = () => {
  currentDate.value = currentDate.value.add(1, 'day');
  // 移除 renderChart()，由 watch 统一处理
};

const handleDateChange = (value: Date | string) => {
  console.log('handleDateChange triggered!', { value, type: typeof value });
  if (value) {
    // 统一使用 dayjs 处理日期，watch 会自动触发图表重新渲染
    currentDate.value = dayjs(value);
    console.log(
      'currentDate updated to:',
      currentDate.value.format('YYYY-MM-DD'),
    );
  }
};

const handleMetricChange = () => {
  // 移除 renderChart()，由 watch 统一处理
  // 这里可以添加其他逻辑，如果需要的话
};

const handleConnectionToggle = () => {
  showConnection.value = !showConnection.value;
  // 移除 renderChart()，由 watch 统一处理
};

const handleDataToggle = () => {
  showData.value = !showData.value;
  // 移除 renderChart()，由 watch 统一处理
};

const handleTableToggle = () => {
  showTable.value = !showTable.value;
  // 切换到表格时，初始化表格数据
  if (showTable.value) {
    initTableData();
  }
  // 切换回图表的逻辑已经在 watch(showTable) 中处理
};

// 初始化表格数据
const initTableData = () => {
  const times = timeData.value;
  const todayData = generateLoadRateData();
  const yesterdayData = generateLoadRateData();

  const newData = times.map((time, index) => ({
    time,
    today: todayData[index]?.value?.toFixed(2) || '--',
    yesterday: yesterdayData[index]?.value?.toFixed(2) || '--',
  }));

  if (gridApi) {
    gridApi.setGridOptions({ data: newData });
  }
};
</script>

<template>
  <Modal
    class="w-[1000px]"
    :title="props.title"
    confirm-text="确定"
    cancel-text="取消"
    content-class="overflow-hidden chart-modal"
  >
    <div class="flex h-[40vh] w-full flex-col gap-4">
      <!-- 顶部工具栏 -->
      <div class="flex items-center justify-between border-b pb-4">
        <!-- 左侧：日期控制 -->
        <div class="flex items-center gap-3">
          <span class="text-sm font-medium">日期</span>
          <ElDatePicker
            v-model="datePickerValue"
            type="date"
            placeholder="选择日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            @change="handleDateChange"
            class="w-40"
          />
          <ElButton @click="handlePreviousDay" size="default" type="primary">
            <IconifyIcon icon="lucide:chevron-left" class="h-4 w-4" />
            上一日
          </ElButton>
          <ElButton @click="handleNextDay" size="default" type="primary">
            下一日
            <IconifyIcon icon="lucide:chevron-right" class="h-4 w-4" />
          </ElButton>
        </div>

        <!-- 右侧：显示控制和指标选择 -->
        <div class="flex items-center gap-3">
          <PrimaryButton
            :class="{ 'bg-yellow-500': showConnection }"
            size="default"
            @click="handleConnectionToggle"
          >
            连线
          </PrimaryButton>
          <PrimaryButton
            :class="{ 'bg-blue-500': showData }"
            size="default"
            @click="handleDataToggle"
          >
            数据
          </PrimaryButton>
          <PrimaryButton
            :class="{ 'bg-green-500': showTable }"
            size="default"
            @click="handleTableToggle"
          >
            <IconifyIcon
              :icon="showTable ? 'lucide:bar-chart-3' : 'lucide:table'"
              class="mr-1 h-4 w-4"
            />
            {{ showTable ? '图表' : '表格' }}
          </PrimaryButton>
          <div class="w-32">
            <ElSelect
              v-model="selectedMetric"
              placeholder="请选择指标"
              @change="handleMetricChange"
            >
              <ElOption
                v-for="option in loadRateOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </ElSelect>
          </div>
        </div>
      </div>

      <!-- 图表/表格区域 -->
      <div class="flex-1">
        <!-- 图表显示 -->
        <EchartsUI v-if="!showTable" ref="chartRef" height="100%" />

        <!-- 表格显示 -->
        <div v-if="showTable" class="h-full overflow-hidden">
          <Grid class="h-full" />
        </div>
      </div>
    </div>
  </Modal>
</template>

<style scoped>
.chart-modal :deep(.el-date-editor) {
  height: 32px;
}

.chart-modal :deep(.el-select) {
  height: 32px;
}

.chart-modal :deep(.el-button) {
  height: 32px;
}

/* 表格容器样式 - 修复高度增长问题 */
.chart-modal :deep(.vxe-grid) {
  height: 100% !important;
  max-height: 400px !important;
  overflow: hidden !important;
}

/* 表格主体样式 */
.chart-modal :deep(.vxe-table) {
  height: 100% !important;
  max-height: 350px !important;
}

/* 表格内容区域 - 控制滚动 */
.chart-modal :deep(.vxe-table--main-wrapper) {
  max-height: 300px !important;
  overflow-y: auto !important;
}

/* 表格头部固定 */
.chart-modal :deep(.vxe-table--header-wrapper) {
  position: sticky !important;
  top: 0 !important;
  z-index: 10 !important;
}

/* 表格列自适应宽度 */
.chart-modal :deep(.vxe-table--header-column),
.chart-modal :deep(.vxe-table--body-column) {
  width: auto !important;
  min-width: 100px !important;
}
</style>
