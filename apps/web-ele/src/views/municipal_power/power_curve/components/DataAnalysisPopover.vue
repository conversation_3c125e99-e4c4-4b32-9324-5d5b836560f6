<script lang="ts" setup>
import type { PowerCurveData } from '../data';

import { computed, ref, watch } from 'vue';

import { IconifyIcon } from '@vben/icons';

import { ElButton, ElPopover } from 'element-plus';

import { useVbenVxeGrid } from '#/adapter/vxe-table';

// Props定义
interface Props {
  tableData?: PowerCurveData[]; // 表格数据源
}

const props = withDefaults(defineProps<Props>(), {
  tableData: () => [],
});

// Popover显示状态
const popoverVisible = ref(false);

// 创建VXE Grid实例
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: {
    columns: [
      {
        field: 'parameter',
        title: '参数',
        width: 80,
        align: 'center',
      },
      {
        title: '最大值',
        align: 'center',
        children: [
          {
            field: 'maxValue',
            title: '数值',
            width: 80,
            align: 'center',
          },
          {
            field: 'maxTime',
            title: '发生时间',
            width: 100,
            align: 'center',
          },
        ],
      },
      {
        title: '最小值',
        align: 'center',
        children: [
          {
            field: 'minValue',
            title: '数值',
            width: 80,
            align: 'center',
          },
          {
            field: 'minTime',
            title: '发生时间',
            width: 100,
            align: 'center',
          },
        ],
      },
      {
        field: 'avgValue',
        title: '平均值',
        width: 80,
        align: 'center',
      },
    ],
    keepSource: true,
    showFooter: false,
    autoResize: true,
    columnConfig: {
      resizable: true,
      useKey: true,
    },
    pagerConfig: {
      enabled: false,
    },
    border: true,
    stripe: false,
    size: 'mini',
  },
});

// 计算数值分析数据
const analysisData = computed(() => {
  if (!props.tableData || props.tableData.length === 0) {
    return [];
  }

  const data = props.tableData;

  // 计算Pa的统计数据
  const paValues = data.map((item) => item.pa);
  const paMax = Math.max(...paValues);
  const paMin = Math.min(...paValues);
  const paAvg = paValues.reduce((sum, val) => sum + val, 0) / paValues.length;
  const paMaxIndex = paValues.indexOf(paMax);
  const paMinIndex = paValues.indexOf(paMin);

  // 计算Pb的统计数据
  const pbValues = data.map((item) => item.pb);
  const pbMax = Math.max(...pbValues);
  const pbMin = Math.min(...pbValues);
  const pbAvg = pbValues.reduce((sum, val) => sum + val, 0) / pbValues.length;
  const pbMaxIndex = pbValues.indexOf(pbMax);
  const pbMinIndex = pbValues.indexOf(pbMin);

  // 计算Pc的统计数据
  const pcValues = data.map((item) => item.pc);
  const pcMax = Math.max(...pcValues);
  const pcMin = Math.min(...pcValues);
  const pcAvg = pcValues.reduce((sum, val) => sum + val, 0) / pcValues.length;
  const pcMaxIndex = pcValues.indexOf(pcMax);
  const pcMinIndex = pcValues.indexOf(pcMin);

  // 计算P的统计数据
  const pValues = data.map((item) => item.p);
  const pMax = Math.max(...pValues);
  const pMin = Math.min(...pValues);
  const pAvg = pValues.reduce((sum, val) => sum + val, 0) / pValues.length;
  const pMaxIndex = pValues.indexOf(pMax);
  const pMinIndex = pValues.indexOf(pMin);

  // 格式化时间
  const formatTime = (timestamp: number) => {
    const date = new Date(timestamp);
    return date.toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return [
    {
      parameter: 'Pa',
      maxValue: paMax.toFixed(2),
      maxTime: formatTime(data[paMaxIndex].date),
      minValue: paMin.toFixed(2),
      minTime: formatTime(data[paMinIndex].date),
      avgValue: paAvg.toFixed(2),
    },
    {
      parameter: 'Pb',
      maxValue: pbMax.toFixed(2),
      maxTime: formatTime(data[pbMaxIndex].date),
      minValue: pbMin.toFixed(2),
      minTime: formatTime(data[pbMinIndex].date),
      avgValue: pbAvg.toFixed(2),
    },
    {
      parameter: 'Pc',
      maxValue: pcMax.toFixed(2),
      maxTime: formatTime(data[pcMaxIndex].date),
      minValue: pcMin.toFixed(2),
      minTime: formatTime(data[pcMinIndex].date),
      avgValue: pcAvg.toFixed(2),
    },
    {
      parameter: 'P',
      maxValue: pMax.toFixed(2),
      maxTime: formatTime(data[pMaxIndex].date),
      minValue: pMin.toFixed(2),
      minTime: formatTime(data[pMinIndex].date),
      avgValue: pAvg.toFixed(2),
    },
  ];
});

// 监听数据变化，更新表格
watch(
  analysisData,
  (newData) => {
    gridApi.setState({
      gridOptions: {
        data: newData,
      },
    });
  },
  { immediate: true },
);
</script>

<template>
  <ElPopover
    popper-class="p-0-popover"
    v-model:visible="popoverVisible"
    placement="bottom-end"
    :width="540"
    trigger="hover"
    :show-after="200"
    :hide-after="100"
  >
    <template #reference>
      <ElButton
        class="[html[data-theme='tech-blue'].dark_&]:bg-accent-foreground"
        type="primary"
      >
        <IconifyIcon icon="lucide:bar-chart-2" class="h-4 w-4" />
        最值分析
      </ElButton>
    </template>

    <div class="data-analysis-popover">
      <Grid />
    </div>
  </ElPopover>
</template>

<style scoped lang="scss">
.data-analysis-popover {
}
</style>
<style>
.p-0-popover {
  padding: 0 !important;
}
</style>
