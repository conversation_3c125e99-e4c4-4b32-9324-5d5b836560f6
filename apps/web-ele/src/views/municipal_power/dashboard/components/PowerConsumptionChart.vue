<script lang="ts" setup>
import { computed, onMounted, ref, watch } from 'vue';

import {
  EchartsUI,
  type EchartsUIType,
  useEcharts,
} from '@vben/plugins/echarts';

interface Props {
  chartType?: 'hourly' | 'total';
}

const props = withDefaults(defineProps<Props>(), {
  chartType: 'hourly',
});

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);

// 24小时时间轴
const hourlyTimeAxis = [
  '00:00', '01:00', '02:00', '03:00', '04:00', '05:00',
  '06:00', '07:00', '08:00', '09:00', '10:00', '11:00',
  '12:00', '13:00', '14:00', '15:00', '16:00', '17:00',
  '18:00', '19:00', '20:00', '21:00', '22:00', '23:00'
];

// 分时段时间轴
const periodTimeAxis = ['尖峰', '峰时', '平时', '谷时', '深谷'];

// 模拟今日24小时用电数据
const todayHourlyData = [
  62, 58, 55, 52, 50, 48, 52, 58, 65, 72, 78, 82,
  85, 88, 86, 84, 82, 85, 87, 85, 80, 75, 70, 65
];

// 模拟昨日24小时用电数据
const yesterdayHourlyData = [
  60, 56, 53, 50, 48, 46, 50, 55, 62, 68, 75, 80,
  83, 86, 84, 82, 80, 83, 85, 83, 78, 73, 68, 63
];

// 模拟今日分时段用电数据
const todayPeriodData = [88, 85, 75, 48, 45];

// 模拟昨日分时段用电数据
const yesterdayPeriodData = [86, 83, 73, 46, 43];

// 计算图表配置
const chartOptions = computed(() => {
  const isHourly = props.chartType === 'hourly';
  const timeAxis = isHourly ? hourlyTimeAxis : periodTimeAxis;
  const todayData = isHourly ? todayHourlyData : todayPeriodData;
  const yesterdayData = isHourly ? yesterdayHourlyData : yesterdayPeriodData;
  const maxValue = Math.max(...todayData, ...yesterdayData);
  const yAxisMax = Math.ceil(maxValue / 10) * 10;

  return {
    toolbox: {
      feature: {
        saveAsImage: {
          title: '保存为图片',
        },
        dataZoom: {
          title: {
            zoom: '区域缩放',
            back: '区域缩放还原',
          },
        },
        restore: {
          title: '还原',
        },
        dataView: {
          title: '数据视图',
          readOnly: false,
        },
      },
      right: '3%',
      top: '8%',
    },
    grid: {
      bottom: '20%',
      containLabel: true,
      left: '3%',
      right: '3%',
      top: '25%',
    },
    legend: {
      bottom: '5%',
      data: ['今日', '昨日'],
      textStyle: {
        fontSize: 11,
      },
    },
    series: [
      {
        name: '今日',
        data: todayData,
        type: 'line',
        smooth: true,
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(34, 197, 94, 0.3)', // green-500 with opacity
              },
              {
                offset: 1,
                color: 'rgba(34, 197, 94, 0.05)', // green-500 with low opacity
              },
            ],
          },
        },
        lineStyle: {
          color: '#22c55e', // green-500
          width: 2,
        },
        itemStyle: {
          color: '#22c55e',
        },
        symbol: 'circle',
        symbolSize: 4,
      },
      {
        name: '昨日',
        data: yesterdayData,
        type: 'line',
        smooth: true,
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(156, 163, 175, 0.3)', // gray-400 with opacity
              },
              {
                offset: 1,
                color: 'rgba(156, 163, 175, 0.05)', // gray-400 with low opacity
              },
            ],
          },
        },
        lineStyle: {
          color: '#9ca3af', // gray-400
          width: 2,
        },
        itemStyle: {
          color: '#9ca3af',
        },
        symbol: 'circle',
        symbolSize: 4,
      },
    ],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        lineStyle: {
          color: '#22c55e',
          width: 1,
        },
      },
      formatter: function (params: any) {
        let result = `${params[0].axisValue}<br/>`;
        params.forEach((param: any) => {
          result += `${param.marker}${param.seriesName}: ${param.value} kWh<br/>`;
        });
        return result;
      },
    },
    xAxis: {
      axisTick: {
        show: false,
      },
      boundaryGap: false,
      data: timeAxis,
      type: 'category',
      axisLabel: {
        fontSize: 11,
        rotate: isHourly ? 45 : 0, // 小时数据时旋转标签
      },
    },
    yAxis: {
      axisTick: {
        show: false,
      },
      type: 'value',
      axisLine: {
        show: false,
      },
      axisLabel: {
        fontSize: 11,
        formatter: '{value} kWh',
      },
      splitLine: {
        lineStyle: {
          color: '#f3f4f6',
          type: 'dashed',
        },
      },
      min: 0,
      max: yAxisMax,
    },
  };
});

// 监听props变化，重新渲染图表
const renderChart = () => {
  renderEcharts(chartOptions.value);
};

onMounted(() => {
  renderChart();
});

// 暴露重新渲染方法
defineExpose({
  renderChart,
});

// 监听chartType变化
watch(() => props.chartType, () => {
  renderChart();
});
</script>

<template>
  <EchartsUI ref="chartRef" height="100%" />
</template>
