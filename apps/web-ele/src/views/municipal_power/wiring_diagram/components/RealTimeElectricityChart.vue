<script lang="ts" setup>
import { onMounted, ref, watch, nextTick } from 'vue';
import { ElRadioGroup, ElRadioButton } from 'element-plus';

import {
  EchartsUI,
  type EchartsUIType,
  useEcharts,
} from '@vben/plugins/echarts';

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);

// Radio切换状态
const chartType = ref<'分时段' | '总用电'>('分时段');

// 预生成静态数据
const dateData = ['08-01', '08-05', '08-09', '08-13', '08-17', '08-21', '08-25', '08-29'];

// 预生成24小时时间数据
const timeData = Array.from({ length: 24 }, (_, i) => {
  return `${i.toString().padStart(2, '0')}:00`;
});

// 预生成用电数据
const yesterdayData = [
  120, 110, 105, 100, 95, 105, 115, 130, 150, 170, 180, 200,
  220, 210, 190, 200, 250, 280, 260, 240, 200, 180, 150, 130
];

const todayData = [
  130, 115, 110, 105, 100, 110, 125, 140, 160, 180, 190, 210,
  230, 220, 200, 210, 260, 300, 270, 250, 210, 190, 160, 140
];

// 分时段堆叠柱状图配置
const renderStackedChart = () => {
  renderEcharts({
    grid: {
      bottom: '20%',
      containLabel: true,
      left: '5%',
      right: '5%',
      top: '15%',
    },
    legend: {
      top: '5%',
      left: 'center',
      data: ['尖', '峰', '平', '谷', '深谷'],
      textStyle: {
        // 使用继承的字体大小
      },
    },
    // Y轴单位标注
    graphic: [
      {
        type: 'text',
        left: '3%',
        top: '12%',
        style: {
          text: 'kW·h',
          fill: '#666',
        },
      },
    ],
    series: [
      {
        name: '尖',
        type: 'bar',
        stack: '用电量',
        data: [800, 750, 900, 650, 600, 720, 820, 780],
        itemStyle: {
          color: '#ef4444', // 红色
        },
      },
      {
        name: '峰',
        type: 'bar',
        stack: '用电量',
        data: [1200, 1100, 1350, 950, 850, 1080, 1230, 1170],
        itemStyle: {
          color: '#f97316', // 橙色
        },
      },
      {
        name: '平',
        type: 'bar',
        stack: '用电量',
        data: [1500, 1400, 1650, 1200, 1100, 1350, 1520, 1450],
        itemStyle: {
          color: '#eab308', // 黄色
        },
      },
      {
        name: '谷',
        type: 'bar',
        stack: '用电量',
        data: [900, 850, 1000, 750, 700, 850, 950, 900],
        itemStyle: {
          color: '#22c55e', // 绿色
        },
      },
      {
        name: '深谷',
        type: 'bar',
        stack: '用电量',
        data: [600, 550, 700, 500, 450, 580, 650, 620],
        itemStyle: {
          color: '#3b82f6', // 蓝色
        },
      },
    ],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter: function (params: any) {
        let result = `${params[0].axisValue}<br/>`;
        params.forEach((param: any) => {
          result += `${param.marker}${param.seriesName}: ${param.value} kW·h<br/>`;
        });
        return result;
      },
    },
    xAxis: {
      type: 'category',
      data: dateData,
      axisLabel: {
        rotate: 45,
      },
      axisTick: {
        show: false,
      },
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value}',
      },
      splitLine: {
        lineStyle: {
          color: '#f3f4f6',
          type: 'dashed',
        },
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
  });
};

// 总用电柱状图配置
const renderTotalChart = () => {

  renderEcharts({
    grid: {
      bottom: '15%',
      containLabel: true,
      left: '0%',
      right: '0%',
      top: '20%', // 为上方图例留出空间
    },
    legend: {
      top: '5%',
      left: 'center',
      data: ['昨日', '今日'],
      textStyle: {
        // 使用继承的字体大小
      },
    },
    // Y轴单位标注
    graphic: [
      {
        type: 'text',
        left: '3%',
        top: '12%',
        style: {
          text: 'kW·h',
          fill: '#666',
        },
      },
    ],
    series: [
      {
        name: '昨日',
        type: 'bar',
        data: yesterdayData,
        barWidth: '40%',
        itemStyle: {
          color: '#9ca3af', // 灰色
        },
      },
      {
        name: '今日',
        type: 'bar',
        data: todayData,
        barWidth: '40%',
        itemStyle: {
          color: '#22c55e', // 绿色
        },
      },
    ],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter: function (params: any) {
        let result = `${params[0].axisValue}<br/>`;
        params.forEach((param: any) => {
          result += `${param.marker}${param.seriesName}: ${param.value} kW·h<br/>`;
        });
        return result;
      },
    },
    xAxis: {
      type: 'category',
      data: timeData,
      axisLabel: {
        interval: 2, // 每3个小时显示一个标签
      },
      axisTick: {
        show: false,
      },
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value}',
      },
      splitLine: {
        lineStyle: {
          color: '#f3f4f6',
          type: 'dashed',
        },
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
      min: 0,
      max: 350,
    },
  });
};

// 根据选择的图表类型渲染对应图表
const renderChart = async () => {
  await nextTick(); // 确保DOM更新完成
  if (chartType.value === '分时段') {
    renderStackedChart();
  } else {
    renderTotalChart();
  }
};

// 监听图表类型变化，使用防抖优化
watch(chartType, () => {
  // 使用setTimeout进行简单的防抖处理
  setTimeout(() => {
    renderChart();
  }, 10);
});

onMounted(async () => {
  await nextTick(); // 确保组件完全挂载
  renderChart();
});
</script>

<template>
  <div class="flex h-full flex-col">
    <!-- Radio切换按钮 -->
    <div class="mb-3 flex justify-center">
      <ElRadioGroup v-model="chartType" size="small">
        <ElRadioButton value="分时段">分时段</ElRadioButton>
        <ElRadioButton value="总用电">总用电</ElRadioButton>
      </ElRadioGroup>
    </div>
    <!-- 图表区域 -->
    <div class="flex-1">
      <EchartsUI ref="chartRef" height="100%" />
    </div>
  </div>
</template>
