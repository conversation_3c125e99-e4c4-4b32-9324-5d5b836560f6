import { ref } from 'vue';
import { useVbenForm } from '#/adapter/form';

/**
 * 工具栏表单字段配置
 * 用于集中管理顶部工具栏的所有表单字段
 * 包括：站点选择器、设备状态、设备类型、节点类型、节点、能源类型、搜索关键词
 */

// 设备状态选项
const deviceStatusOptions = [
  { label: '全部状态', value: '' },
  { label: '在线', value: 'online' },
  { label: '离线', value: 'offline' },
  { label: '故障', value: 'fault' },
];

// 设备类型选项
const deviceTypeOptions = [
  { label: '全部类型', value: '' },
  { label: '变压器', value: 'transformer' },
  { label: '开关柜', value: 'switchgear' },
  { label: '电表', value: 'meter' },
];

// 节点类型选项
const nodeTypeOptions = [
  { label: '全部节点类型', value: '' },
  { label: '变电站', value: 'substation' },
  { label: '配电房', value: 'distribution' },
  { label: '用电设备', value: 'equipment' },
  { label: '监测点', value: 'monitor' },
];

// 节点选项（根据节点类型动态变化）
const nodeOptions = ref([
  { label: '全部节点', value: '' },
  { label: '1号变电站', value: 'substation_1' },
  { label: '2号变电站', value: 'substation_2' },
  { label: '配电房A', value: 'distribution_a' },
  { label: '配电房B', value: 'distribution_b' },
  { label: '生产车间1', value: 'workshop_1' },
  { label: '生产车间2', value: 'workshop_2' },
  { label: '办公楼', value: 'office' },
]);

// 能源类型选项
const energyTypeOptions = [
  { label: '电', value: 'electricity' },
  { label: '气', value: 'gas' },
  { label: '水', value: 'water' },
];

/**
 * 创建工具栏表单配置
 */
export function createToolbarForm() {
  const [ToolbarForm, toolbarFormApi] = useVbenForm({
    // 表单配置
    collapsed: false,
    showDefaultActions: false,
    showCollapseButton: false,
    submitOnChange: true,
    submitOnEnter: true,
    layout: 'horizontal',
    wrapperClass: 'toolbar-form-layout',

    // 通用配置
    commonConfig: {
      componentProps: {
        class: 'w-full',
      },
      controlClass: 'w-full',
      labelWidth: 0,
    },

    // 表单字段配置
    schema: [
      // 站点选择器
      {
        component: 'SiteSelector',
        fieldName: 'selectedSite',
        label: '',
        componentProps: {
          placeholder: '请选择企业',
          class: 'w-48 sm:w-52',
        },
      },

      // 设备状态选择器
      {
        component: 'Select',
        fieldName: 'deviceStatus',
        label: '',
        componentProps: {
          placeholder: '全部状态',
          options: deviceStatusOptions,
          class: 'sm:!w-full md:!w-28 lg:!w-32',
        },
      },

      // 设备类型选择器
      {
        component: 'Select',
        fieldName: 'deviceType',
        label: '',
        componentProps: {
          placeholder: '全部类型',
          options: deviceTypeOptions,
          class: 'sm:!w-full md:!w-28 lg:!w-32',
        },
      },

      // 节点类型选择器
      {
        component: 'Select',
        fieldName: 'nodeType',
        label: '',
        componentProps: {
          placeholder: '节点类型',
          options: nodeTypeOptions,
          class: 'sm:!w-full md:!w-32 lg:!w-36',
          onChange: (value: string) => {
            // 根据节点类型更新节点选项
            updateNodeOptions(value);
            // 清空节点选择
            toolbarFormApi?.setFieldValue('node', '');
          },
        },
      },

      // 节点选择器
      {
        component: 'Select',
        fieldName: 'node',
        label: '',
        componentProps: {
          placeholder: '节点',
          options: nodeOptions,
          class: 'sm:!w-full md:!w-32 lg:!w-36',
        },
      },

      // 能源类型选择器
      {
        component: 'Select',
        fieldName: 'energyType',
        label: '',
        componentProps: {
          placeholder: '能源类型',
          options: energyTypeOptions,
          class: 'sm:!w-full md:!w-32 lg:!w-36',
        },
      },

      // 搜索输入框
      {
        component: 'Input',
        fieldName: 'searchKeyword',
        label: '',
        componentProps: {
          placeholder: '网关识别号/仪表型号/回路名称',
          class: 'sm:!w-full md:!w-48 lg:!w-64',
          clearable: true,
          suffixIcon: 'lucide:search',
        },
      },
    ],

    // 表单提交处理
    handleSubmit: (values: any) => {
      console.log('工具栏表单提交:', values);
      // 这里可以触发数据过滤逻辑
      handleToolbarFormSubmit(values);
    },
  });

  /**
   * 根据节点类型更新节点选项
   */
  function updateNodeOptions(nodeType: string) {
    let newOptions = [{ label: '全部节点', value: '' }];

    switch (nodeType) {
      case 'substation':
        newOptions = [
          { label: '全部变电站', value: '' },
          { label: '1号变电站', value: 'substation_1' },
          { label: '2号变电站', value: 'substation_2' },
          { label: '3号变电站', value: 'substation_3' },
        ];
        break;
      case 'distribution':
        newOptions = [
          { label: '全部配电房', value: '' },
          { label: '配电房A', value: 'distribution_a' },
          { label: '配电房B', value: 'distribution_b' },
          { label: '配电房C', value: 'distribution_c' },
        ];
        break;
      case 'equipment':
        newOptions = [
          { label: '全部用电设备', value: '' },
          { label: '生产车间1', value: 'workshop_1' },
          { label: '生产车间2', value: 'workshop_2' },
          { label: '办公楼', value: 'office' },
          { label: '宿舍楼', value: 'dormitory' },
        ];
        break;
      case 'monitor':
        newOptions = [
          { label: '全部监测点', value: '' },
          { label: '监测点1', value: 'monitor_1' },
          { label: '监测点2', value: 'monitor_2' },
          { label: '监测点3', value: 'monitor_3' },
        ];
        break;
      default:
        newOptions = [
          { label: '全部节点', value: '' },
          { label: '1号变电站', value: 'substation_1' },
          { label: '2号变电站', value: 'substation_2' },
          { label: '配电房A', value: 'distribution_a' },
          { label: '配电房B', value: 'distribution_b' },
          { label: '生产车间1', value: 'workshop_1' },
          { label: '生产车间2', value: 'workshop_2' },
          { label: '办公楼', value: 'office' },
        ];
    }

    nodeOptions.value = newOptions;

    // 更新表单字段的选项
    toolbarFormApi?.updateSchema([
      {
        fieldName: 'node',
        componentProps: {
          options: newOptions,
        },
      },
    ]);
  }

  /**
   * 处理工具栏表单提交
   */
  function handleToolbarFormSubmit(values: any) {
    // 这里可以添加数据过滤逻辑
    console.log('过滤条件:', values);

    // 可以通过事件或回调函数通知父组件更新数据
    // 例如：emit('filter-change', values);
  }

  /**
   * 获取表单值
   */
  async function getToolbarFormValues() {
    return toolbarFormApi ? await toolbarFormApi.getValues() : {};
  }

  /**
   * 重置表单
   */
  function resetToolbarForm() {
    toolbarFormApi?.resetFields();
  }

  /**
   * 设置表单值
   */
  function setToolbarFormValues(values: any) {
    toolbarFormApi?.setValues(values);
  }

  return {
    ToolbarForm,
    toolbarFormApi,
    getToolbarFormValues,
    resetToolbarForm,
    setToolbarFormValues,
    nodeOptions,
  };
}

/**
 * 工具栏表单数据类型
 */
export interface ToolbarFormData {
  selectedSite?: any; // 站点信息
  deviceStatus?: string; // 设备状态
  deviceType?: string; // 设备类型
  nodeType?: string; // 节点类型
  node?: string; // 节点
  energyType?: string; // 能源类型
  searchKeyword?: string; // 搜索关键词
}
