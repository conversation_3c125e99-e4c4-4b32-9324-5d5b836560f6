<template>
  <div
    class="bg-card border-border group relative cursor-pointer overflow-hidden rounded-lg border transition-all duration-300 hover:scale-[1.02] hover:shadow-lg [html[data-theme='tech-blue'].dark_&]:border-blue-500/20 [html[data-theme='tech-blue'].dark_&]:bg-[#2e4294]"
    @click="handleClick"
  >
    <!-- 状态指示条 -->
    <div :class="statusBarClass" class="absolute left-0 top-0 h-1 w-full"></div>

    <!-- 卡片内容 - 响应式内边距 -->
    <div class="p-3 sm:p-4">
      <!-- 头部：设备图标和名称 - 响应式布局 -->
      <div class="mb-2 flex items-center justify-between sm:mb-3">
        <div class="flex items-center gap-2 sm:gap-3">
          <div
            :class="deviceIconBgClass"
            class="flex h-8 w-8 items-center justify-center rounded-lg sm:h-10 sm:w-10"
          >
            <IconifyIcon
              :icon="deviceIcon"
              class="h-4 w-4 text-white sm:h-6 sm:w-6"
            />
          </div>
          <div class="min-w-0 flex-1">
            <div
              class="text-foreground truncate text-sm font-medium sm:text-base"
            >
              {{ device.name }}
            </div>
            <div class="text-muted-foreground truncate text-xs">
              {{ device.location }}
            </div>
          </div>
        </div>

        <!-- 状态指示器 - 响应式 -->
        <div class="flex shrink-0 items-center gap-1">
          <div :class="statusDotClass" class="h-2 w-2 rounded-full"></div>
          <span :class="statusTextClass" class="text-xs font-medium">
            {{ statusText }}
          </span>
        </div>
      </div>

      <!-- 设备信息 - 响应式文字大小 -->
      <div class="mb-2 space-y-1 sm:mb-3">
        <div class="flex justify-between text-xs sm:text-sm">
          <span class="text-muted-foreground">设备地址:</span>
          <span class="text-foreground ml-2 truncate font-mono">{{
            device.address
          }}</span>
        </div>
        <div class="flex justify-between text-xs sm:text-sm">
          <span class="text-muted-foreground">最后更新:</span>
          <span class="text-foreground ml-2 truncate text-xs">{{
            formatTime(device.lastUpdate)
          }}</span>
        </div>
      </div>

      <!-- 关键数据 - 响应式网格 -->
      <div class="mb-3 grid grid-cols-2 gap-1 sm:mb-4 sm:gap-2">
        <div
          v-for="(value, key) in displayValues"
          :key="key"
          class="bg-muted/20 [html[data-theme='tech-blue'].dark_&]:bg-accent-foreground/10 rounded p-1 text-center sm:p-2"
        >
          <div class="text-muted-foreground text-xs">
            {{ getValueLabel(key) }}
          </div>
          <div class="text-foreground text-xs font-medium sm:text-sm">
            {{ formatValue(key, value) }}
          </div>
        </div>
      </div>

      <!-- 操作按钮 - 响应式布局，优化色彩和高度 -->
      <div class="flex gap-2">
        <ElButton
          type="primary"
          size="default"
          class="hidden h-8 flex-1 text-xs sm:h-9 sm:text-sm"
          @click.stop="handleDetail"
        >
          <IconifyIcon icon="lucide:eye" class="mr-1 h-3 w-3 sm:h-4 sm:w-4" />
          <span>详情</span>
        </ElButton>
        <ElButton
          type="info"
          size="default"
          class="hidden bg-accent-foreground h-8 px-3 sm:h-9 sm:px-4"
          @click.stop="handleControl"
        >
          <IconifyIcon
            icon="lucide:settings"
            class="text-primary dark:text-foreground h-3 w-3 sm:h-4 sm:w-4"
          />
        </ElButton>
      </div>
    </div>

    <!-- 悬停效果 -->
    <div
      class="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100 [html[data-theme='tech-blue'].dark_&]:via-blue-400/5"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { ElButton } from 'element-plus';
import { IconifyIcon } from '@vben/icons';

interface DeviceData {
  id: string;
  name: string;
  type: 'transformer' | 'switchgear' | 'meter';
  address: string;
  status: 'online' | 'offline' | 'fault';
  location: string;
  lastUpdate: string;
  values: {
    voltage?: number;
    current?: number;
    power?: number;
    temperature?: number;
    [key: string]: any;
  };
}

interface Props {
  device: DeviceData;
}

interface Emits {
  (e: 'click', device: DeviceData): void;
  (e: 'detail', device: DeviceData): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 设备图标映射
const deviceIcon = computed(() => {
  const icons = {
    transformer: 'lucide:zap',
    switchgear: 'lucide:power',
    meter: 'lucide:gauge',
  };
  return icons[props.device.type];
});

// 设备图标背景色
const deviceIconBgClass = computed(() => {
  const colors = {
    transformer: 'bg-blue-500',
    switchgear: 'bg-green-500',
    meter: 'bg-purple-500',
  };
  return colors[props.device.type];
});

// 状态相关样式
const statusBarClass = computed(() => {
  const colors = {
    online: 'bg-green-500',
    offline: 'bg-gray-400',
    fault: 'bg-red-500',
  };
  return colors[props.device.status];
});

const statusDotClass = computed(() => {
  const colors = {
    online: 'bg-green-500',
    offline: 'bg-gray-400',
    fault: 'bg-red-500',
  };
  return colors[props.device.status];
});

const statusTextClass = computed(() => {
  const colors = {
    online: 'text-green-600',
    offline: 'text-gray-500',
    fault: 'text-red-600',
  };
  return colors[props.device.status];
});

const statusText = computed(() => {
  const texts = {
    online: '在线',
    offline: '离线',
    fault: '故障',
  };
  return texts[props.device.status];
});

// 显示的数值（只显示前4个）
const displayValues = computed(() => {
  const values = props.device.values;
  const entries = Object.entries(values).slice(0, 4);
  return Object.fromEntries(entries);
});

// 数值标签映射
const getValueLabel = (key: string) => {
  const labels: Record<string, string> = {
    voltage: '电压',
    current: '电流',
    power: '功率',
    temperature: '温度',
    frequency: '频率',
    energy: '电能',
  };
  return labels[key] || key;
};

// 数值格式化
const formatValue = (key: string, value: any) => {
  if (typeof value !== 'number') return value;

  const units: Record<string, string> = {
    voltage: 'V',
    current: 'A',
    power: 'kW',
    temperature: '°C',
    frequency: 'Hz',
    energy: 'kWh',
  };

  const unit = units[key] || '';
  return `${value}${unit}`;
};

// 时间格式化
const formatTime = (timeStr: string) => {
  const date = new Date(timeStr);
  const now = new Date();
  const diff = now.getTime() - date.getTime();

  if (diff < 60000) return '刚刚';
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`;

  return date.toLocaleDateString();
};

// 事件处理
const handleClick = () => {
  emit('click', props.device);
};

const handleDetail = () => {
  emit('detail', props.device);
};

const handleControl = () => {
  console.log('Control device:', props.device.id);
};
</script>

<style scoped>
/* 自定义样式可以在这里添加 */
</style>
