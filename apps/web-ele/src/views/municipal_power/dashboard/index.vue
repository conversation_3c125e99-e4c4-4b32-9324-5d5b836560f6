<script setup lang="ts">
import { ref } from 'vue';

import { AnalysisChartCard, Page } from '@vben/common-ui';
import {
  DesktopComputer,
  ElectricCord1,
  GraphDotFlat,
  InfrastructureFlat,
  StreamlinePlumpColorCameraVideo,
  StreamlinePlumpColorDashboardGauge2,
  StreamlinePlumpColorlayers1,
  StreamlinePlumpColorWifiFlat,
  StreamlineSharpColorEcoHouseFlat,
} from '@vben/icons';

// MeteoconsBarometerFill,
//   FluentColorBuilding20,
import SearchSelector from '#/components/services/search-selector/SearchSelector.vue';
import MarqueeScroll from '#/components/ui/marquee/MarqueeScroll.vue';
import type { MarqueeItem } from '#/components/ui/marquee/MarqueeScroll.vue';
import PowerConsumptionChart from './components/PowerConsumptionChart.vue';

defineOptions({
  name: 'MunicipalPowerDashboard',
});

// 图表类型控制
const chartType = ref<'hourly' | 'total'>('hourly');

// 跑马灯数据源
const marqueeData: MarqueeItem[] = [
  {
    id: 1,
    message: '变压器T1温度异常，当前温度85℃，超过安全阈值',
    level: 'error',
    status: 'error',
  },
  {
    id: 2,
    message: '配电柜C3通信中断，已持续15分钟',
    level: 'warning',
    status: 'warning',
  },
  {
    id: 3,
    message: '楼栋B2负载率达到92%，接近满载状态',
    level: 'warning',
    status: 'warning',
  },
  {
    id: 4,
    message: '系统自动切换至备用电源，运行正常',
    level: 'info',
    status: 'info',
  },
];
</script>

<template>
  <Page auto-content-height>
    <div class="flex h-full flex-col gap-4 lg:overflow-auto">
      <!-- 上面一行：响应式布局 -->
      <div class="flex flex-col lg:flex-row h-auto lg:h-1/2 gap-4">
        <!-- 第一列：概况 -->
        <AnalysisChartCard title="概况" class="flex-1 min-h-[400px] lg:min-h-0">
          <div class="flex h-full flex-col">
            <div class="mb-4 flex items-center gap-x-4">
              <span>变配电站名称:</span>
              <SearchSelector class="max-w-[70%]" />
            </div>
            <!-- 6个指标卡片 - 响应式网格布局 -->
            <div class="grid min-h-0 flex-1 grid-cols-2 sm:grid-cols-3 lg:grid-cols-3 gap-3">
              <!-- 第一行 -->
              <div
                class="border-border flex flex-col rounded-lg border [html[data-theme='tech-blue'].dark_&]:bg-[#2e4294]"
              >
                <div
                  class="text-muted-foreground flex h-[70%] flex-col items-center justify-center text-center"
                >
                  <StreamlinePlumpColorDashboardGauge2
                    class="mb-1 size-8 transition-transform duration-300 hover:scale-110 sm:size-12 md:size-12 lg:size-20"
                  />
                  <div>电压等级</div>
                </div>
                <div
                  class="bg-muted text-muted-foreground [html[data-theme='tech-blue'].dark_&]:bg-accent-foreground flex h-[30%] w-full items-center justify-center rounded text-sm font-medium"
                >
                  10.0.4kV
                </div>
              </div>

              <div
                class="border-border flex flex-col rounded-lg border [html[data-theme='tech-blue'].dark_&]:bg-[#2e4294]"
              >
                <div
                  class="text-muted-foreground flex h-[70%] flex-col items-center justify-center text-center"
                >
                  <InfrastructureFlat
                    class="mb-1 size-8 transition-transform duration-300 hover:scale-110 sm:size-12 md:size-12 lg:size-20"
                  />
                  <div>变压器台数</div>
                </div>
                <div
                  class="bg-muted text-muted-foreground [html[data-theme='tech-blue'].dark_&]:bg-accent-foreground flex h-[30%] w-full items-center justify-center rounded text-sm font-medium"
                >
                  —台
                </div>
              </div>

              <div
                class="border-border flex flex-col rounded-lg border [html[data-theme='tech-blue'].dark_&]:bg-[#2e4294]"
              >
                <div
                  class="text-muted-foreground flex h-[70%] flex-col items-center justify-center text-center"
                >
                  <GraphDotFlat
                    class="mb-1 size-8 transition-transform duration-300 hover:scale-110 sm:size-12 md:size-12 lg:size-16"
                  />
                  <div>负载率</div>
                </div>
                <div
                  class="bg-muted text-muted-foreground [html[data-theme='tech-blue'].dark_&]:bg-accent-foreground flex h-[30%] w-full items-center justify-center rounded text-xs font-medium"
                >
                  —%
                </div>
              </div>

              <!-- 第二行 -->
              <div
                class="border-border flex flex-col rounded-lg border [html[data-theme='tech-blue'].dark_&]:bg-[#2e4294]"
              >
                <div
                  class="text-muted-foreground flex h-[70%] flex-col items-center justify-center text-center"
                >
                  <ElectricCord1
                    class="mb-1 size-8 transition-transform duration-300 hover:scale-110 sm:size-12 md:size-12 lg:size-16"
                  />
                  <div>额定容量</div>
                </div>
                <div
                  class="bg-muted text-muted-foreground [html[data-theme='tech-blue'].dark_&]:bg-accent-foreground flex h-[30%] w-full items-center justify-center rounded text-xs font-medium"
                >
                  —kVA
                </div>
              </div>

              <div
                class="border-border flex flex-col rounded-lg border [html[data-theme='tech-blue'].dark_&]:bg-[#2e4294]"
              >
                <div
                  class="text-muted-foreground flex h-[70%] flex-col items-center justify-center text-center"
                >
                  <StreamlinePlumpColorlayers1
                    class="mb-1 size-8 transition-transform duration-300 hover:scale-110 sm:size-12 md:size-12 lg:size-16"
                  />
                  <div>最大需量</div>
                </div>
                <div
                  class="bg-muted text-muted-foreground [html[data-theme='tech-blue'].dark_&]:bg-accent-foreground flex h-[30%] w-full items-center justify-center rounded text-xs font-medium"
                >
                  —kW
                </div>
              </div>

              <div
                class="border-border flex flex-col rounded-lg border [html[data-theme='tech-blue'].dark_&]:bg-[#2e4294]"
              >
                <div
                  class="text-muted-foreground flex h-[70%] flex-col items-center justify-center text-center"
                >
                  <DesktopComputer
                    class="mb-1 size-8 transition-transform duration-300 hover:scale-110 sm:size-10 md:size-10 lg:size-16"
                  />
                  <div>测量装置</div>
                </div>
                <div
                  class="bg-muted text-muted-foreground [html[data-theme='tech-blue'].dark_&]:bg-accent-foreground flex h-[30%] w-full items-center justify-center rounded text-xs font-medium"
                >
                  42个
                </div>
              </div>
            </div>
          </div>
        </AnalysisChartCard>

        <!-- 第二列：站点状态 -->
        <AnalysisChartCard title="运行状态" class="flex-1 min-h-[400px] lg:min-h-0">
          <div class="flex h-full flex-col">
            <!-- 更新时间 -->
            <div class="text-muted-foreground mb-4 text-sm">
              更新时间 2025-07-11 12:35:00
            </div>

            <div class="flex min-h-0 flex-1 flex-col">
              <!-- 4个圆形彩色图标区域 -->
              <div class="grid flex-1 grid-cols-2 sm:grid-cols-4 gap-4">
                <!-- 有功功率 -->
                <div class="flex flex-col items-center justify-center">
                  <div
                    class="lg:h-18 lg:w-18 mb-2 flex h-12 w-12 items-center justify-center rounded-full bg-red-500 sm:h-14 sm:w-14 md:h-16 md:w-16"
                  >
                    <span
                      class="icon-[mdi--chart-line] text-lg text-white sm:text-xl md:text-2xl lg:text-3xl"
                    ></span>
                  </div>
                  <div class="mb-1 text-center">有功功率</div>
                  <div class="text-sm font-medium">341.4kW</div>
                </div>

                <!-- 无功功率 -->
                <div class="flex flex-col items-center justify-center">
                  <div
                    class="lg:h-18 lg:w-18 mb-2 flex h-12 w-12 items-center justify-center rounded-full bg-blue-500 sm:h-14 sm:w-14 md:h-16 md:w-16"
                  >
                    <span
                      class="icon-[mdi--shield-check] text-lg text-white sm:text-xl md:text-2xl lg:text-3xl"
                    ></span>
                  </div>
                  <div class="mb-1 text-center">无功功率</div>
                  <div class="text-sm font-medium">126.9kVar</div>
                </div>

                <!-- 环境温度 -->
                <div class="flex flex-col items-center justify-center">
                  <div
                    class="lg:h-18 lg:w-18 mb-2 flex h-12 w-12 items-center justify-center rounded-full bg-green-500 sm:h-14 sm:w-14 md:h-16 md:w-16"
                  >
                    <span
                      class="icon-[mdi--thermometer] text-lg text-white sm:text-xl md:text-2xl lg:text-3xl"
                    ></span>
                  </div>
                  <div class="mb-1 text-center">环境温度</div>
                  <div class="text-sm font-medium">30.2℃</div>
                </div>

                <!-- 环境湿度 -->
                <div class="flex flex-col items-center justify-center">
                  <div
                    class="lg:h-18 lg:w-18 mb-2 flex h-12 w-12 items-center justify-center rounded-full bg-yellow-500 sm:h-14 sm:w-14 md:h-16 md:w-16"
                  >
                    <span
                      class="icon-[mdi--water-percent] text-lg text-white sm:text-xl md:text-2xl lg:text-3xl"
                    ></span>
                  </div>
                  <div class="mb-1 text-center">环境湿度</div>
                  <div class="text-sm font-medium">66.5%</div>
                </div>
              </div>

              <!-- 底部4个方形区域 -->
              <div class="grid flex-1 grid-cols-2 sm:grid-cols-4 gap-4">
                <!-- 配电图 -->
                <div
                  class="border-border flex flex-col rounded-lg border [html[data-theme='tech-blue'].dark_&]:bg-[#2e4294]"
                >
                  <div
                    class="text-muted-foreground flex h-[70%] flex-col items-center justify-center text-center text-sm"
                  >
                    <StreamlineSharpColorEcoHouseFlat
                      class="mb-1 size-8 transition-transform duration-300 hover:scale-110 sm:size-10 md:size-10 lg:size-16"
                    />
                  </div>
                  <div
                    class="bg-muted text-muted-foreground [html[data-theme='tech-blue'].dark_&]:bg-accent-foreground flex h-[30%] w-full items-center justify-center rounded text-sm font-medium"
                  >
                    配电图
                  </div>
                </div>

                <!-- 视频 -->
                <div
                  class="border-border flex flex-col rounded-lg border [html[data-theme='tech-blue'].dark_&]:bg-[#2e4294]"
                >
                  <div
                    class="text-muted-foreground flex h-[70%] flex-col items-center justify-center text-center text-sm"
                  >
                    <StreamlinePlumpColorCameraVideo
                      class="mb-1 size-8 transition-transform duration-300 hover:scale-110 sm:size-10 md:size-10 lg:size-16"
                    />
                  </div>
                  <div
                    class="bg-muted text-muted-foreground [html[data-theme='tech-blue'].dark_&]:bg-accent-foreground flex h-[30%] w-full items-center justify-center rounded text-sm font-medium"
                  >
                    视频
                  </div>
                </div>

                <!-- 变压器 -->
                <div
                  class="border-border flex flex-col rounded-lg border [html[data-theme='tech-blue'].dark_&]:bg-[#2e4294]"
                >
                  <div
                    class="text-muted-foreground flex h-[70%] flex-col items-center justify-center text-center text-sm"
                  >
                    <InfrastructureFlat
                      class="mb-1 size-8 transition-transform duration-300 hover:scale-110 sm:size-10 md:size-10 lg:size-16"
                    />
                  </div>
                  <div
                    class="bg-muted text-muted-foreground [html[data-theme='tech-blue'].dark_&]:bg-accent-foreground flex h-[30%] w-full items-center justify-center rounded text-sm font-medium"
                  >
                    变压器
                  </div>
                </div>

                <!-- 通讯 -->
                <div
                  class="border-border flex flex-col rounded-lg border [html[data-theme='tech-blue'].dark_&]:bg-[#2e4294]"
                >
                  <div
                    class="text-muted-foreground flex h-[70%] flex-col items-center justify-center text-center text-sm"
                  >
                    <StreamlinePlumpColorWifiFlat
                      class="mb-1 size-8 transition-transform duration-300 hover:scale-110 sm:size-10 md:size-10 lg:size-16"
                    />
                  </div>
                  <div
                    class="bg-muted text-muted-foreground [html[data-theme='tech-blue'].dark_&]:bg-accent-foreground flex h-[30%] w-full items-center justify-center rounded text-sm font-medium"
                  >
                    通讯
                  </div>
                </div>
              </div>
            </div>
          </div>
        </AnalysisChartCard>

        <!-- 第三列：当日事件记录 -->
        <div class="flex flex-1 flex-col gap-4 min-h-[400px] lg:min-h-0">
          <!-- 上半部分 -->
          <AnalysisChartCard title="事件统计" class="flex-1">
            <div class="flex h-full flex-col">
              <!-- 三个彩色统计卡片 -->
              <div class="grid min-h-0 flex-1 grid-cols-1 sm:grid-cols-3 gap-3">
                <!-- 运行超限 -->
                <div class="flex h-full flex-col">
                  <div
                    class="rounded-t bg-gradient-to-br from-green-400 to-green-600 py-3 text-center text-sm font-medium text-white"
                  >
                    运行超限
                  </div>
                  <div
                    class="bg-card border-border flex flex-1 items-center justify-center rounded-b border border-t-0"
                  >
                    <span class="text-2xl font-bold">0次</span>
                  </div>
                </div>

                <!-- 通信异位 -->
                <div class="flex h-full flex-col">
                  <div
                    class="rounded-t bg-gradient-to-br from-red-400 to-red-600 py-3 text-center text-sm font-medium text-white"
                  >
                    通信异位
                  </div>
                  <div
                    class="bg-card border-border flex flex-1 items-center justify-center rounded-b border border-t-0"
                  >
                    <span class="text-2xl font-bold">2次</span>
                  </div>
                </div>

                <!-- 失联异常 -->
                <div class="flex h-full flex-col">
                  <div
                    class="rounded-t bg-gradient-to-br from-blue-400 to-blue-600 py-3 text-center text-sm font-medium text-white"
                  >
                    失联异常
                  </div>
                  <div
                    class="bg-card border-border flex flex-1 items-center justify-center rounded-b border border-t-0"
                  >
                    <span class="text-2xl font-bold">12次</span>
                  </div>
                </div>
              </div>
            </div>
          </AnalysisChartCard>

          <!-- 下半部分 -->
          <AnalysisChartCard title="告警记录" class="flex-1">
            <div class="flex h-full flex-col">
              <!-- 跑马灯组件 -->
              <div class="flex-1 overflow-hidden">
                <div class="flex h-full flex-col justify-center">
                  <div
                    class="relative h-full overflow-hidden rounded-lg bg-muted/20 [html[data-theme='tech-blue'].dark_&]:bg-[#2e4294] p-3"
                  >
                    <MarqueeScroll
                      :data-source="marqueeData"
                      direction="up"
                      :speed="20"
                      :pause-on-hover="true"
                      height="100%"
                      width="100%"
                    />
                  </div>
                </div>
              </div>
            </div>
          </AnalysisChartCard>
        </div>
      </div>

      <!-- 下面一行：响应式两列布局 -->
      <div class="flex flex-col lg:flex-row h-auto lg:h-1/2 gap-4">
        <!-- 第一列：主要图表 -->
        <AnalysisChartCard title="当日逐时用电曲线" class="w-full lg:w-2/3 min-h-[400px] lg:min-h-0">
          <template #title-actions>
            <div class="flex items-center gap-1 sm:gap-2">
              <button
                @click="chartType = 'hourly'"
                class="px-2 sm:px-3 py-1 text-xs sm:text-sm rounded-md border border-border hover:bg-muted transition-colors"
                :class="{
                  'bg-primary text-primary-foreground': chartType === 'hourly',
                  'bg-background text-foreground': chartType !== 'hourly'
                }"
              >
                分时段
              </button>
              <button
                @click="chartType = 'total'"
                class="px-2 sm:px-3 py-1 text-xs sm:text-sm rounded-md border border-border hover:bg-muted transition-colors"
                :class="{
                  'bg-primary text-primary-foreground': chartType === 'total',
                  'bg-background text-foreground': chartType !== 'total'
                }"
              >
                总用电
              </button>
            </div>
          </template>

          <div class="h-full">
            <PowerConsumptionChart :chart-type="chartType" />
          </div>
        </AnalysisChartCard>

        <!-- 第二列：统计信息 -->
        <AnalysisChartCard title="用电概况" class="w-full lg:w-1/3 min-h-[400px] lg:min-h-0">
          <div class="flex h-full flex-col gap-3 p-4">
            <!-- 上半部分：三个统计卡片 -->
            <div class="flex flex-1 flex-col gap-3 min-h-0">
              <!-- 当月用电 -->
              <div class="flex items-stretch overflow-hidden rounded-lg flex-1">
                <div class="bg-purple-500 text-white px-3 py-2 text-sm font-medium min-w-[70px] sm:min-w-[80px] flex items-center justify-center rounded-l-lg">
                  当月用电
                </div>
                <div class="bg-card border-border [html[data-theme='tech-blue'].dark_&]:border-purple-500/20 border border-l-0 flex-1 px-3 py-2 flex items-center justify-center rounded-r-lg">
                  <span class="text-base sm:text-lg font-bold">70462 kW·h</span>
                </div>
              </div>

              <!-- 上月同期 -->
              <div class="flex items-stretch overflow-hidden rounded-lg flex-1">
                <div class="bg-cyan-500 text-white px-3 py-2 text-sm font-medium min-w-[70px] sm:min-w-[80px] flex items-center justify-center rounded-l-lg">
                  上月同期
                </div>
                <div class="bg-card border-border [html[data-theme='tech-blue'].dark_&]:border-cyan-500/20 border border-l-0 flex-1 px-3 py-2 flex items-center justify-center rounded-r-lg">
                  <span class="text-base sm:text-lg font-bold">26992 kW·h</span>
                </div>
              </div>

              <!-- 环比 -->
              <div class="flex items-stretch overflow-hidden rounded-lg flex-1">
                <div class="bg-lime-500 text-white px-3 py-2 text-sm font-medium min-w-[70px] sm:min-w-[80px] flex items-center justify-center rounded-l-lg">
                  环比
                </div>
                <div class="bg-card border-border [html[data-theme='tech-blue'].dark_&]:border-lime-500/20 border border-l-0 flex-1 px-3 py-2 flex items-center justify-center rounded-r-lg">
                  <div class="flex items-center gap-1">
                    <span class="text-base sm:text-lg font-bold text-red-600">161.05%</span>
                    <span class="icon-[mdi--trending-up] text-red-500 text-lg"></span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 下半部分：最大用电时间段 -->
            <div class="bg-muted [html[data-theme='tech-blue'].dark_&]:bg-accent-foreground rounded-lg p-3 flex items-center justify-between">
              <div class="text-center flex-1">
                <div class="text-foreground text-base sm:text-lg font-bold">08:15-08:30</div>
                <div class="text-muted-foreground text-xs sm:text-sm">最大用电时间</div>
              </div>
              <div class="text-center flex-1">
                <div class="text-foreground text-base sm:text-lg font-bold">352kW</div>
                <div class="text-muted-foreground text-xs sm:text-sm">该时段平均功率</div>
              </div>
            </div>
          </div>
        </AnalysisChartCard>
      </div>
    </div>
  </Page>
</template>

<style scoped lang="scss"></style>
