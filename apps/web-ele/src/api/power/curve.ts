import { requestClient } from '#/api/request';

/**
 * 电力曲线报表数据类型定义
 */
export interface PowerCurveData {
  circuitName: string; // 回路名称
  date: number; // 采集时间（时间戳）
  pa: number; // Pa(kW) - A相功率
  pb: number; // Pb(kW) - B相功率
  pc: number; // Pc(kW) - C相功率
  p: number; // P(kW) - 总功率
}

/**
 * 电力曲线报表查询参数（业务参数）
 */
export interface PowerCurveQueryParams {
  startTime?: string; // 开始时间
  endTime?: string; // 结束时间
  reportType?: 'rawData'; // 报表类型
  timeInterval?: string; // 时间间隔 (1:一分钟, 5:五分钟, 15:十五分钟, 30:半小时, 60:一小时)
  electricalCategory?: string; // 电力类别 (1:功率, 2:电流, 3:相电压, 4:线电压, 5:不平衡度, 6:电压谐波, 7:电流谐波)
  // 右侧表单的checkbox参数（0:不勾选, 1:勾选）
  showPhaseA?: number; // 显示A相
  showPhaseB?: number; // 显示B相
  showPhaseC?: number; // 显示C相
  showTotalPower?: number; // 显示总有功功率
  // 左侧筛选表单的参数
  enterpriseId?: string; // 企业ID
  energyType?: string; // 能源类型 (1:电, 2:气, 3:水等)
  siteId?: string; // 站点ID
  keyword?: string; // 关键字
  circuitNames?: string[]; // 选中的回路ID列表（复数形式）
}

/**
 * 分页参数
 */
export interface PaginationParams {
  page: number; // 页码
  pageSize: number; // 每页数量
}

/**
 * 完整的请求参数结构
 */
export interface PowerCurveRequestParams {
  query: PowerCurveQueryParams; // 查询参数
  pagination: PaginationParams; // 分页参数
}

/**
 * 电力曲线报表响应数据
 */
export interface PowerCurveResponse {
  items: PowerCurveData[];
  total: number;
  page: number;
  pageSize: number;
  reportType?: string;
}

/**
 * 获取电力曲线报表数据（POST方法）
 * @param requestParams 请求参数（包含query和pagination）
 * @returns 电力曲线报表数据
 */
export async function getPowerCurveApi(
  requestParams: PowerCurveRequestParams,
): Promise<PowerCurveResponse> {
  return requestClient.post('/power/curve', requestParams);
}
