import type { EChartsOption } from 'echarts';

import type { Ref } from 'vue';

import type { Nullable } from '@vben/types';

import type EchartsUI from './echarts-ui.vue';

import { computed, nextTick, watch } from 'vue';

import { usePreferences } from '@vben/preferences';

import {
  tryOnUnmounted,
  useDebounceFn,
  useResizeObserver,
  useTimeoutFn,
  useWindowSize,
} from '@vueuse/core';

import echarts from './echarts';

type EchartsUIType = typeof EchartsUI | undefined;

type EchartsThemeType = 'dark' | 'light' | null;

function useEcharts(chartRef: Ref<EchartsUIType>) {
  let chartInstance: echarts.ECharts | null = null;
  let cacheOptions: EChartsOption = {};

  const { isDark } = usePreferences();
  const { height, width } = useWindowSize();
  const resizeHandler: () => void = useDebounceFn(resize, 200);

  const getOptions = computed((): EChartsOption => {
    const textColor = isDark.value ? '#ffffff' : '#374151';

    return {
      backgroundColor: 'transparent',
      textStyle: {
        color: textColor,
      },
      // 图例配置
      legend: {
        textStyle: {
          color: textColor,
        },
      },
      // 标题配置
      title: {
        textStyle: {
          color: textColor,
        },
      },
    };
  });

  const initCharts = (t?: EchartsThemeType) => {
    const el = chartRef?.value?.$el;
    if (!el) {
      return;
    }
    chartInstance = echarts.init(el, t || isDark.value ? 'dark' : null);

    return chartInstance;
  };

  // 深度合并对象的辅助函数
  const deepMerge = (target: any, source: any): any => {
    const result = { ...target };
    for (const key in source) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = deepMerge(target[key] || {}, source[key]);
      } else {
        result[key] = source[key];
      }
    }
    return result;
  };

  const renderEcharts = (
    options: EChartsOption,
    clear = true,
    retryCount = 0,
    incrementalUpdate = false, // 新增参数：是否为增量更新
  ): Promise<Nullable<echarts.ECharts>> => {
    cacheOptions = options;

    // 获取基础主题配置
    const baseThemeOptions = getOptions.value;

    // 如果用户配置中包含坐标轴，则添加坐标轴主题配置
    const axisThemeOptions: EChartsOption = {};
    if (options.xAxis || options.yAxis) {
      const textColor = isDark.value ? '#ffffff' : '#374151';
      const axisLineColor = isDark.value ? '#4b5563' : '#e5e7eb';
      const splitLineColor = isDark.value ? '#374151' : '#f3f4f6';

      axisThemeOptions.xAxis = {
        axisLabel: {
          color: textColor,
        },
        axisLine: {
          lineStyle: {
            color: axisLineColor,
          },
        },
      };

      axisThemeOptions.yAxis = {
        axisLabel: {
          color: textColor,
        },
        axisLine: {
          lineStyle: {
            color: axisLineColor,
          },
        },
        splitLine: {
          lineStyle: {
            color: splitLineColor,
          },
        },
      };
    }

    // 深度合并：基础主题 -> 坐标轴主题 -> 用户配置
    const currentOptions = deepMerge(
      deepMerge(baseThemeOptions, axisThemeOptions),
      options
    );

    return new Promise((resolve, reject) => {
      const el = chartRef?.value?.$el;

      // 检查DOM元素是否存在且可见
      if (!el || el.offsetHeight === 0 || el.offsetWidth === 0) {
        if (retryCount < 10) {
          // 最多重试10次，每次间隔50ms
          useTimeoutFn(async () => {
            try {
              const result = await renderEcharts(currentOptions, clear, retryCount + 1);
              resolve(result);
            } catch (error) {
              reject(error);
            }
          }, 50);
          return;
        } else {
          console.warn('ECharts: DOM element not ready after 10 retries');
          resolve(null);
          return;
        }
      }

      nextTick(() => {
        useTimeoutFn(() => {
          try {
            // 如果实例不存在或已被销毁，重新创建
            if (!chartInstance || chartInstance.isDisposed()) {
              chartInstance = initCharts();
              if (!chartInstance) {
                console.error('ECharts: Failed to initialize chart instance');
                resolve(null);
                return;
              }
            }

            // 根据更新类型选择不同的策略
            if (incrementalUpdate && chartInstance && !chartInstance.isDisposed()) {
              // 增量更新：只更新数据相关部分，不清空图表
              console.log('ECharts: Performing incremental update');
              const incrementalOptions = {
                title: options.title,
                series: options.series,
                // 只包含会变化的部分，保持其他配置不变
              };
              chartInstance.setOption(incrementalOptions, false); // notMerge = false，增量更新
            } else {
              // 完全重建：用于弹窗打开或首次渲染
              console.log('ECharts: Performing full rebuild');
              if (clear) {
                chartInstance.clear();
              }
              chartInstance.setOption(currentOptions); // 完整配置
            }

            resolve(chartInstance);
          } catch (error) {
            console.error('ECharts: Error during chart rendering:', error);
            // 如果渲染失败，尝试重新初始化实例
            if (retryCount < 3) {
              disposeChart();
              renderEcharts(currentOptions, clear, retryCount + 1, incrementalUpdate)
                .then(resolve)
                .catch(reject);
            } else {
              reject(error);
            }
          }
        }, 30);
      });
    });
  };

  function resize() {
    chartInstance?.resize({
      animation: {
        duration: 300,
        easing: 'quadraticIn',
      },
    });
  }

  watch([width, height], () => {
    resizeHandler?.();
  });

  useResizeObserver(chartRef as never, resizeHandler);

  watch(isDark, () => {
    if (chartInstance) {
      chartInstance.dispose();
      initCharts();
      renderEcharts(cacheOptions);
      resize();
    }
  });

  // 主动清理图表实例
  const disposeChart = () => {
    if (chartInstance) {
      chartInstance.dispose();
      chartInstance = null;
      cacheOptions = {};
    }
  };

  // 强制重新初始化图表实例
  const reinitChart = (theme?: EchartsThemeType) => {
    disposeChart();
    return initCharts(theme);
  };

  tryOnUnmounted(() => {
    // 销毁实例，释放资源
    disposeChart();
  });

  return {
    renderEcharts,
    resize,
    getChartInstance: () => chartInstance,
    disposeChart,
    reinitChart,
  };
}

export { useEcharts };

export type { EchartsUIType };
