<template>
  <Page auto-content-height>
    <div class="flex h-full flex-col gap-4">
      <!-- 顶部工具栏 - 使用集中管理的表单 -->
      <div
        class="bg-card border-border flex min-h-[45px] flex-col gap-3 rounded-lg border p-4 py-2 lg:flex-row lg:items-center lg:gap-4 lg:py-0"
      >
        <!-- 筛选表单区域 -->
        <div class="flex-1">
          <ToolbarForm />
        </div>

        <!-- 右侧操作按钮 -->
        <div class="flex gap-2 lg:ml-auto">
          <ElButton
            type="default"
            class="flex-1 sm:flex-none"
            @click="refreshData"
          >
            <IconifyIcon icon="lucide:refresh-cw" class="mr-1 h-4 w-4" />
            <span class="hidden sm:inline">刷新</span>
          </ElButton>
          <ElButton
            type="primary"
            class="flex-1 sm:flex-none"
            @click="exportData"
          >
            <IconifyIcon icon="lucide:download" class="mr-1 h-4 w-4" />
            <span class="hidden sm:inline">导出</span>
          </ElButton>
        </div>
      </div>

      <!-- 主要内容区域 - 响应式布局 -->
      <div class="flex min-h-0 flex-1 flex-col gap-4 xl:flex-row">
        <!-- 左侧统计面板 - 响应式宽度 -->
        <div class="w-full shrink-0 xl:w-80">
          <AnalysisChartCard title="设备概况" class="h-full">
            <div class="flex h-full flex-col gap-4">
              <!-- 统计卡片 - 响应式网格 -->
              <div class="grid grid-cols-1 gap-3 sm:grid-cols-2 xl:grid-cols-2">
                <!-- 设备总数 -->
                <div
                  class="rounded-lg bg-gradient-to-br from-blue-500 to-blue-600 p-3 text-white sm:p-4"
                >
                  <div class="flex items-center justify-between">
                    <div>
                      <div class="text-xs opacity-90 sm:text-sm">设备总数</div>
                      <div class="text-xl font-bold sm:text-2xl">
                        {{ statistics.totalDevices }}
                      </div>
                    </div>
                    <IconifyIcon
                      icon="lucide:server"
                      class="h-6 w-6 opacity-80 sm:h-8 sm:w-8"
                    />
                  </div>
                </div>

                <!-- 在线设备 -->
                <div
                  class="rounded-lg bg-gradient-to-br from-green-500 to-green-600 p-3 text-white sm:p-4"
                >
                  <div class="flex items-center justify-between">
                    <div>
                      <div class="text-xs opacity-90 sm:text-sm">在线设备</div>
                      <div class="text-xl font-bold sm:text-2xl">
                        {{ statistics.onlineDevices }}
                      </div>
                    </div>
                    <IconifyIcon
                      icon="lucide:wifi"
                      class="h-6 w-6 opacity-80 sm:h-8 sm:w-8"
                    />
                  </div>
                </div>

                <!-- 离线设备 -->
                <div
                  class="rounded-lg bg-gradient-to-br from-red-500 to-red-600 p-3 text-white sm:p-4"
                >
                  <div class="flex items-center justify-between">
                    <div>
                      <div class="text-xs opacity-90 sm:text-sm">离线设备</div>
                      <div class="text-xl font-bold sm:text-2xl">
                        {{ statistics.offlineDevices }}
                      </div>
                    </div>
                    <IconifyIcon
                      icon="lucide:wifi-off"
                      class="h-6 w-6 opacity-80 sm:h-8 sm:w-8"
                    />
                  </div>
                </div>

                <!-- 故障设备 -->
                <div
                  class="rounded-lg bg-gradient-to-br from-orange-500 to-orange-600 p-3 text-white sm:p-4"
                >
                  <div class="flex items-center justify-between">
                    <div>
                      <div class="text-xs opacity-90 sm:text-sm">故障设备</div>
                      <div class="text-xl font-bold sm:text-2xl">
                        {{ statistics.faultDevices }}
                      </div>
                    </div>
                    <IconifyIcon
                      icon="lucide:alert-triangle"
                      class="h-6 w-6 opacity-80 sm:h-8 sm:w-8"
                    />
                  </div>
                </div>
              </div>

              <!-- 在线率显示 -->
              <div
                class="bg-muted/20 rounded-lg p-3 sm:p-4 [html[data-theme='tech-blue'].dark_&]:bg-[#2e4294]"
              >
                <div class="text-center">
                  <div class="text-muted-foreground mb-2 text-xs sm:text-sm">
                    设备在线率
                  </div>
                  <div class="text-primary text-2xl font-bold sm:text-3xl">
                    {{ onlineRate }}%
                  </div>
                  <div class="text-muted-foreground mt-1 text-xs">
                    更新时间: {{ updateTime }}
                  </div>
                </div>
              </div>

              <!-- 今日用电指标 - 响应式布局 -->
              <div class="space-y-2 sm:space-y-3">
                <!-- 今日用电 -->
                <div
                  class="flex flex-col overflow-hidden rounded-lg sm:flex-row sm:items-stretch"
                >
                  <div
                    class="flex items-center justify-center rounded-t-lg bg-blue-500 px-3 py-2 text-xs font-medium text-white sm:min-w-[80px] sm:rounded-l-lg sm:rounded-t-lg sm:text-sm"
                  >
                    今日用电
                  </div>
                  <div
                    class="bg-card border-border flex flex-1 items-center justify-center rounded-b-lg border border-t-0 px-3 py-2 sm:rounded-b-lg sm:rounded-r-lg sm:border-l-0 sm:border-t [html[data-theme='tech-blue'].dark_&]:border-blue-500/20"
                  >
                    <span class="text-sm font-bold sm:text-base"
                      >{{ energyStats.todayPower }} kW·h</span
                    >
                  </div>
                </div>

                <!-- 今日综合能耗 -->
                <div
                  class="flex flex-col overflow-hidden rounded-lg sm:flex-row sm:items-stretch"
                >
                  <div
                    class="flex items-center justify-center rounded-t-lg bg-green-500 px-3 py-2 text-xs font-medium text-white sm:min-w-[80px] sm:rounded-l-lg sm:rounded-t-lg sm:text-sm"
                  >
                    综合能耗
                  </div>
                  <div
                    class="bg-card border-border flex flex-1 items-center justify-center rounded-b-lg border border-t-0 px-3 py-2 sm:rounded-b-lg sm:rounded-r-lg sm:border-l-0 sm:border-t [html[data-theme='tech-blue'].dark_&]:border-green-500/20"
                  >
                    <span class="text-sm font-bold sm:text-base"
                      >{{ energyStats.todayConsumption }} kW·h</span
                    >
                  </div>
                </div>

                <!-- 今日碳排放量 -->
                <div
                  class="flex flex-col overflow-hidden rounded-lg sm:flex-row sm:items-stretch"
                >
                  <div
                    class="flex items-center justify-center rounded-t-lg bg-orange-500 px-3 py-2 text-xs font-medium text-white sm:min-w-[80px] sm:rounded-l-lg sm:rounded-t-lg sm:text-sm"
                  >
                    碳排放量
                  </div>
                  <div
                    class="bg-card border-border flex flex-1 items-center justify-center rounded-b-lg border border-t-0 px-3 py-2 sm:rounded-b-lg sm:rounded-r-lg sm:border-l-0 sm:border-t [html[data-theme='tech-blue'].dark_&]:border-orange-500/20"
                  >
                    <span class="text-sm font-bold sm:text-base"
                      >{{ energyStats.todayCarbon }} kg</span
                    >
                  </div>
                </div>
              </div>

              <!-- 设备类型分布 -->
              <div class="min-h-0 flex-1">
                <div
                  class="text-foreground mb-2 text-xs font-medium sm:mb-3 sm:text-sm"
                >
                  设备类型分布
                </div>
                <div class="space-y-1 sm:space-y-2">
                  <div
                    v-for="item in deviceTypeStats"
                    :key="item.type"
                    class="flex items-center justify-between"
                  >
                    <div class="flex items-center gap-2">
                      <div
                        :class="item.color"
                        class="h-2 w-2 rounded-full sm:h-3 sm:w-3"
                      ></div>
                      <span class="text-xs sm:text-sm">{{ item.name }}</span>
                    </div>
                    <span class="text-xs font-medium sm:text-sm">{{
                      item.count
                    }}</span>
                  </div>
                </div>
              </div>
            </div>
          </AnalysisChartCard>
        </div>

        <!-- 右侧设备监控区域 - 标题和tabs在同一行 -->
        <div class="min-h-0 flex-1">
          <AnalysisChartCard title="设备监控" class="h-full">
            <template #title-actions>
              <!-- 视图切换标签页 - 优化边框和字体颜色 -->
              <div
                class="bg-muted/30 border-border inline-flex h-9 items-center justify-center rounded-lg border p-1 [html[data-theme=tech-blue].dark_&]:border-blue-400/40 [html[data-theme=tech-blue].dark_&]:bg-blue-900/20"
              >
                <button
                  v-for="tab in viewTabs"
                  :key="tab.value"
                  :class="[
                    'focus-visible:ring-ring inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
                    activeViewTab === tab.value
                      ? 'bg-accent-foreground [html[data-theme=tech-blue].dark_&]:bg-accent-foreground shadow-sm [html[data-theme=tech-blue].dark_&]:shadow-blue-500/20'
                      : 'hover:bg-background/60 [html[data-theme=tech-blue].dark_&]:hover:bg-blue-800/30',
                  ]"
                  @click="activeViewTab = tab.value"
                >
                  <IconifyIcon :icon="tab.icon" class="mr-1 h-3 w-3" />
                  <span class="hidden sm:inline">{{
                    tab.label.replace('视图', '')
                  }}</span>
                </button>
              </div>
            </template>

            <div class="view-container h-full overflow-auto">
              <!-- 卡片视图 -->
              <div v-if="activeViewTab === 'card'" class="h-full">
                <!-- 响应式网格：手机1列，平板2列，桌面3-5列 -->
                <div
                  class="3xl:grid-cols-5 grid grid-cols-1 gap-3 p-2 sm:grid-cols-2 sm:gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 2xl:grid-cols-4"
                >
                  <DeviceCard
                    v-for="device in filteredDevices"
                    :key="device.id"
                    :device="device"
                    @click="handleDeviceClick"
                    @detail="handleDeviceDetail"
                  />
                </div>

                <!-- 空状态提示 -->
                <div
                  v-if="filteredDevices.length === 0"
                  class="flex h-full items-center justify-center"
                >
                  <div class="text-center">
                    <IconifyIcon
                      icon="lucide:server-off"
                      class="text-muted-foreground mx-auto mb-4 h-12 w-12"
                    />
                    <div class="text-muted-foreground text-sm">
                      暂无设备数据
                    </div>
                  </div>
                </div>
              </div>

              <!-- 表格视图 -->
              <div v-else-if="activeViewTab === 'table'" class="h-full">
                <DeviceGrid class="h-full" />
              </div>
            </div>
          </AnalysisChartCard>
        </div>
      </div>
    </div>
  </Page>
</template>

<script setup lang="ts">
import type { TabOption } from '@vben/types';

import { ref, computed, onMounted, provide } from 'vue';
import { useRouter } from 'vue-router';
import { AnalysisChartCard, Page } from '@vben/common-ui';
import { ElButton } from 'element-plus';
import { IconifyIcon } from '@vben/icons';
import DeviceCard from './components/DeviceCard.vue';
import DeviceGrid from './components/DeviceGrid.vue';
import { createToolbarForm, type ToolbarFormData } from './toolbar-form-config';

defineOptions({
  name: 'DataMonitoring',
});

// 路由实例
const router = useRouter();

// 接口类型定义
interface DeviceData {
  id: string;
  name: string;
  type: 'transformer' | 'switchgear' | 'meter';
  address: string;
  status: 'online' | 'offline' | 'fault';
  location: string;
  lastUpdate: string;
  values: {
    voltage?: number;
    current?: number;
    power?: number;
    temperature?: number;
    [key: string]: any;
  };
}

interface Statistics {
  totalDevices: number;
  onlineDevices: number;
  offlineDevices: number;
  faultDevices: number;
}

interface EnergyStats {
  todayPower: number;
  todayConsumption: number;
  todayCarbon: number;
}

// 响应式数据
const devices = ref<DeviceData[]>([]);
const updateTime = ref('');

// 创建工具栏表单
const {
  ToolbarForm,
  toolbarFormApi,
  getToolbarFormValues,
  resetToolbarForm,
  setToolbarFormValues,
} = createToolbarForm();

// 视图切换标签页配置
const viewTabs = ref<TabOption[]>([
  {
    label: '卡片视图',
    value: 'card',
    icon: 'lucide:layout-grid',
  },
  {
    label: '表格视图',
    value: 'table',
    icon: 'lucide:table-2',
  },
]);

const activeViewTab = ref('card');

// 统计数据
const statistics = ref<Statistics>({
  totalDevices: 0,
  onlineDevices: 0,
  offlineDevices: 0,
  faultDevices: 0,
});

// 能耗统计数据
const energyStats = ref<EnergyStats>({
  todayPower: 0,
  todayConsumption: 0,
  todayCarbon: 0,
});

// 设备类型统计
const deviceTypeStats = ref([
  { type: 'transformer', name: '变压器', count: 0, color: 'bg-blue-500' },
  { type: 'switchgear', name: '开关柜', count: 0, color: 'bg-green-500' },
  { type: 'meter', name: '电表', count: 0, color: 'bg-purple-500' },
]);

// 计算属性
const onlineRate = computed(() => {
  if (statistics.value.totalDevices === 0) return 0;
  return Math.round(
    (statistics.value.onlineDevices / statistics.value.totalDevices) * 100,
  );
});

const filteredDevices = computed(() => {
  return devices.value.filter((device) => {
    // 这里暂时返回所有设备，过滤逻辑将在工具栏表单提交时处理
    // 实际的过滤逻辑会在 handleToolbarFormSubmit 中实现
    return true;
  });
});

// 事件处理
const handleDeviceClick = (device: DeviceData) => {
  console.log('Device clicked:', device);
  // 跳转到详情页，传递设备ID
  router.push({
    name: 'DataMonitoringDetail',
    params: { id: device.id }
  });
};

const handleDeviceDetail = (device: DeviceData) => {
  console.log('Device detail:', device);
  // 也可以跳转到详情页
  handleDeviceClick(device);
};

const refreshData = () => {
  loadDeviceData();
};

const exportData = () => {
  console.log('Export data');
};

// 数据加载
const loadDeviceData = () => {
  // 模拟数据加载
  const mockDevices: DeviceData[] = [];

  // 生成模拟设备数据
  for (let i = 1; i <= 50; i++) {
    const types: DeviceData['type'][] = ['transformer', 'switchgear', 'meter'];
    const statuses: DeviceData['status'][] = ['online', 'offline', 'fault'];
    const type = types[Math.floor(Math.random() * types.length)];
    const status = statuses[Math.floor(Math.random() * statuses.length)];

    mockDevices.push({
      id: `device-${i}`,
      name: `${getTypeName(type)}${i.toString().padStart(2, '0')}`,
      type,
      address: `192.168.1.${i}`,
      status,
      location: `${Math.floor(i / 10) + 1}号楼`,
      lastUpdate: new Date(
        Date.now() - Math.random() * 3600000,
      ).toLocaleString(),
      values: {
        voltage: Math.round(220 + Math.random() * 20),
        current: Math.round(Math.random() * 100),
        power: Math.round(Math.random() * 1000),
        temperature: Math.round(25 + Math.random() * 15),
      },
    });
  }

  devices.value = mockDevices;
  updateStatistics();
  updateEnergyStats();
  updateTime.value = new Date().toLocaleString();
};

const getTypeName = (type: DeviceData['type']) => {
  const names = {
    transformer: '变压器',
    switchgear: '开关柜',
    meter: '电表',
  };
  return names[type];
};

const updateStatistics = () => {
  const total = devices.value.length;
  const online = devices.value.filter((d) => d.status === 'online').length;
  const offline = devices.value.filter((d) => d.status === 'offline').length;
  const fault = devices.value.filter((d) => d.status === 'fault').length;

  statistics.value = {
    totalDevices: total,
    onlineDevices: online,
    offlineDevices: offline,
    faultDevices: fault,
  };

  // 更新设备类型统计
  deviceTypeStats.value.forEach((stat) => {
    stat.count = devices.value.filter((d) => d.type === stat.type).length;
  });
};

const updateEnergyStats = () => {
  // 模拟今日能耗数据
  energyStats.value = {
    todayPower: Math.round(15000 + Math.random() * 5000), // 今日用电 15000-20000 kW·h
    todayConsumption: Math.round(18000 + Math.random() * 6000), // 今日综合能耗 18000-24000 kW·h
    todayCarbon: Math.round(8000 + Math.random() * 2000), // 今日碳排放量 8000-10000 kg
  };
};

// 向子组件提供数据
provide('devices', devices);
provide('filteredDevices', filteredDevices);

// 生命周期
onMounted(() => {
  loadDeviceData();
});
</script>

<style scoped lang="scss">
/* 工具栏表单布局样式 */
:deep(.toolbar-form-layout) {
  display: flex !important;
  flex-wrap: wrap !important;
  align-items: center !important;
  //gap: 0.5rem 1rem !important;
  row-gap: 1rem !important;
  column-gap: 0 !important;
  justify-content: flex-start !important;

  /* 大屏幕不换行，表单项水平排列 */
  @media (min-width: 1024px) {
    //flex-wrap: nowrap !important;
    //gap: 0.5rem 1.5rem !important;
    row-gap: 1.5rem !important;
    column-gap: 0 !important;
  }

  /* 中等屏幕适当换行 */
  @media (max-width: 1023px) and (min-width: 640px) {
    //gap: 0.5rem 1rem !important;
    row-gap: 1rem !important;
    column-gap: 0 !important;
  }

  /* 小屏幕垂直排列 */
  @media (max-width: 639px) {
    flex-direction: column !important;
    align-items: stretch !important;
    //gap: 0.5rem !important;
    row-gap: 1rem !important;
    column-gap: 0 !important;
  }

  /* 表单项容器样式 */
  > * {
    flex: 0 0 auto !important;
    margin-bottom: 0 !important;

    /* 小屏幕下占满宽度 */
    @media (max-width: 639px) {
      flex: 1 1 100% !important;
      width: 100% !important;
    }
  }

  /* 隐藏表单项的标签 */
  .vben-form-item-label {
    display: none !important;
  }

  /* 调整表单控件样式 */
  .vben-form-item-control {
    width: 100% !important;
  }
}
:deep(.toolbar-form-layout > div.pb-6) {
  padding-bottom: 0 !important;
}
/* 修复滚动条样式 - tech-blue 主题 */
html[data-theme='tech-blue'].dark {
  .view-container {
    /* 滚动条整体样式 */
    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
      background-color: hsl(var(--card)); /* or add it to the track */
    }

    /* 滚动条轨道 */
    &::-webkit-scrollbar-track {
      background-color: transparent;
      border-radius: 4px;
    }

    /* 滚动条滑块 */
    &::-webkit-scrollbar-thumb {
      background: #4477dd;
      border-radius: 4px;
      border: 1px solid rgba(255, 255, 255, 0.1);

      &:hover {
        background: #5588ee;
      }

      &:active {
        background: #3366cc;
      }
    }

    /* 滚动条角落 */
    &::-webkit-scrollbar-corner {
      background-color: transparent;
    }
  }
}

/* 默认主题的滚动条样式 */
html:not([data-theme='tech-blue']) {
  .view-container {
    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    &::-webkit-scrollbar-track {
      background-color: hsl(var(--muted));
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: hsl(var(--border));
      border-radius: 4px;

      &:hover {
        background: hsl(var(--accent));
      }
    }
  }
}
:deep(.vxe-grid.p-2) {
  padding: 0 !important;
}
</style>
