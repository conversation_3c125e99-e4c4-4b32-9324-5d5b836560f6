<template>
  <div class="marquee-wrapper" :class="wrapperClass">
    <div class="marquee-container" :style="containerStyle">
      <div
        class="marquee-content"
        :class="{ horizontal: direction === 'left' || direction === 'right' }"
        :style="contentStyle"
        @mouseenter="pauseAnimation"
        @mouseleave="resumeAnimation"
      >
        <template v-if="$slots.default">
          <!-- 使用插槽自定义内容 -->
          <slot
            v-for="(item, index) in dataSource"
            :key="`item-${index}`"
            :item="item"
            :index="index"
          />
        </template>
        <template v-else>
          <!-- 默认渲染 -->
          <div
            v-for="(item, index) in dataSource"
            :key="`default-${index}`"
            class="marquee-item"
          >
            <div class="flex items-center gap-2 whitespace-nowrap">
              <span
                v-if="item.status"
                class="status-dot"
                :class="getStatusClass(item.status)"
              ></span>
              <span
                v-if="item.level"
                class="level-badge"
                :class="getLevelClass(item.level)"
              >
                【{{ getLevelText(item.level) }}】
              </span>
              <span class="message-text">{{
                item.message || item.text || item
              }}</span>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';

// 定义数据项类型
export interface MarqueeItem {
  id?: string | number;
  message?: string;
  text?: string;
  level?: 'info' | 'warning' | 'error' | 'success';
  status?: 'normal' | 'warning' | 'error' | 'info';
  [key: string]: any;
}

// 组件属性
interface Props {
  dataSource: MarqueeItem[] | string[];
  direction?: 'up' | 'down' | 'left' | 'right';
  speed?: number; // 动画持续时间（秒）
  gap?: number; // 项目间距（rem）
  pauseOnHover?: boolean;
  wrapperClass?: string;
  height?: string;
  width?: string;
}

const props = withDefaults(defineProps<Props>(), {
  dataSource: () => [],
  direction: 'up',
  speed: 20,
  gap: 0.75,
  pauseOnHover: true,
  height: '100%',
  width: '100%',
});

// 动画状态
const isPaused = ref(false);

// 计算样式
const containerStyle = computed(() => ({
  height: props.height,
  width: props.width,
}));

const contentStyle = computed(() => {
  const animationPlayState = isPaused.value ? 'paused' : 'running';
  const gap = `${props.gap}rem`;

  return {
    gap,
    '--animation-duration': `${props.speed}s`,
    animationPlayState,
  };
});

// 获取动画名称
function getAnimationName() {
  switch (props.direction) {
    case 'up':
      return 'marquee-up';
    case 'down':
      return 'marquee-down';
    case 'left':
      return 'marquee-left';
    case 'right':
      return 'marquee-right';
    default:
      return 'marquee-up';
  }
}

// 获取状态样式
function getStatusClass(status: string) {
  const statusMap = {
    normal: 'bg-green-500',
    info: 'bg-blue-500',
    warning: 'bg-yellow-500',
    error: 'bg-red-500',
  };
  return statusMap[status as keyof typeof statusMap] || 'bg-gray-500';
}

// 获取级别样式
function getLevelClass(level: string) {
  const levelMap = {
    info: 'text-blue-600',
    success: 'text-green-600',
    warning: 'text-yellow-600',
    error: 'text-red-600',
  };
  return levelMap[level as keyof typeof levelMap] || 'text-gray-600';
}

// 获取级别文本
function getLevelText(level: string) {
  const levelTextMap = {
    info: '信息',
    success: '成功',
    warning: '警告',
    error: '严重',
  };
  return levelTextMap[level as keyof typeof levelTextMap] || level;
}

// 暂停动画
function pauseAnimation() {
  if (props.pauseOnHover) {
    isPaused.value = true;
  }
}

// 恢复动画
function resumeAnimation() {
  if (props.pauseOnHover) {
    isPaused.value = false;
  }
}

// 暴露方法
defineExpose({
  pause: () => {
    isPaused.value = true;
  },
  resume: () => {
    isPaused.value = false;
  },
  toggle: () => {
    isPaused.value = !isPaused.value;
  },
});
</script>

<style scoped>
.marquee-wrapper {
  position: relative;
  overflow: hidden;
  height: 100%;
  width: 100%;
}

.marquee-container {
  position: relative;
  height: 100%;
  width: 100%;
  overflow: hidden;
  display: flex;
  align-items: center;
}

.marquee-content {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.marquee-content:not(.horizontal) {
  animation: marquee-up var(--animation-duration, 20s) linear infinite;
}

.marquee-content.horizontal {
  flex-direction: row;
  align-items: center;
  animation: marquee-left var(--animation-duration, 20s) linear infinite;
}

.marquee-item {
  flex-shrink: 0;
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: var(--foreground);
  padding: 0.5rem 0;
}

.status-dot {
  display: inline-block;
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.level-badge {
  font-weight: 500;
}

.message-text {
  color: inherit;
}

/* 动画定义 */
@keyframes marquee-up {
  0% {
    transform: translateY(100%);
  }
  100% {
    transform: translateY(-100%);
  }
}

@keyframes marquee-down {
  0% {
    transform: translateY(-100%);
  }
  100% {
    transform: translateY(100%);
  }
}

@keyframes marquee-left {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

@keyframes marquee-right {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style>
