import type { useAsideFormData } from '#/composables/useAsideFormData';

import { computed, ref } from 'vue';

import { useVbenForm } from '#/adapter/form';

/**
 * 创建简化的左侧筛选表单（使用集中式数据管理）
 *
 * 设计原则：
 * 1. 单一数据源：所有数据都来自 dataManager
 * 2. 响应式优先：使用 computed 实现自动更新
 * 3. 最小化配置：避免复杂的同步逻辑
 */
export function createSimplifiedAsideForm(
  dataManager: ReturnType<typeof useAsideFormData>,
) {
  // 创建响应式状态来跟踪功能选项
  const checkboxGroupValue = ref<string[]>([]);

  const [Form, formApi] = useVbenForm({
    // 默认展开搜索表单
    collapsed: false,
    // 所有表单项共用配置
    commonConfig: {
      // 所有表单项统一样式
      componentProps: {
        class: 'w-full sm:max-w-xs md:max-w-sm lg:max-w-md xl:max-w-sm mx-4',
      },
      // 控制表单控件的最大宽度，避免在网格布局中过大
      controlClass: 'max-w-xs', // 限制控件最大宽度为 max-w-xs (20rem/320px)
      // labelClass: 'justify-start',
      // labelWidth: 80,
      labelClass: 'ml-4',
      // formItemClass: 'flex-nowrap',
    },
    // 表单项配置
    schema: [
      // 企业选择器
      {
        component: 'SearchSelector',
        fieldName: 'enterpriseId',
        label: '企业',
        componentProps: {
          placeholder: '请选择企业',
          searchPlaceholder: '请输入企业名称搜索',
          buttonType: 'default',
          buttonSize: 'default',
          placement: 'bottom-start',
          popoverWidth: 270,
          filterable: true,
          remote: false,
          loading: dataManager.loading,
          // 响应式数据绑定
          options: dataManager.enterpriseOptions,
          // 简化的事件处理
          onChange: (value: string) => {
            dataManager.handleEnterpriseChange(value);
          },
        },
      },

      // 站点选择器
      {
        component: 'SearchSelector',
        fieldName: 'siteId',
        label: '站点名称',
        componentProps: {
          placeholder: '请先选择企业',
          searchPlaceholder: '请输入站点名称搜索',
          buttonType: 'default',
          buttonSize: 'default',
          placement: 'bottom-start',
          popoverWidth: 270,
          filterable: true,
          remote: false,
          loading: dataManager.loading,
          disabled: computed(() => !dataManager.hasEnterprise.value),
          // 响应式数据绑定
          options: dataManager.siteOptions,
          // 简化的事件处理
          onChange: (value: string) => {
            dataManager.handleSiteChange(value);
          },
        },
      },

      // 能源类型选择器
      {
        component: 'Select',
        fieldName: 'powerType',
        label: '能源类型',
        defaultValue: '1',
        componentProps: {
          placeholder: '请选择能源类型',
          options: [{ label: '电', value: '1' }],
        },
      },

      // 回路筛选输入框
      {
        component: 'Input',
        fieldName: 'circuitFilter',
        label: '',
        componentProps: {
          placeholder: '输入关键字筛选',
          clearable: true,
          // 使用Tree组件内部filter方法，不影响原数据
          onInput: (value: string) => {
            setTimeout(() => {
              const treeComponentRef =
                formApi?.getFieldComponentRef<any>('circuitTree');
              if (treeComponentRef?.filter) {
                treeComponentRef.filter(value);
                if (process.env.NODE_ENV === 'development') {
                  console.log('🔄 Tree筛选关键字:', value);
                }
              }
            }, 50);
          },
        },
      },

      // 功能选项
      {
        component: 'CheckboxGroup',
        fieldName: 'checkboxGroup',
        label: '',
        defaultValue: [],
        componentProps: {
          options: [
            { label: '级联选择', value: 'cascade' },
            { label: '全选', value: 'selectAll' },
          ],
          // 修复的全选和级联处理
          onChange: (values: string[]) => {
            checkboxGroupValue.value = values;

            const isSelectAll = values.includes('selectAll');
            const isCascade = values.includes('cascade');

            if (process.env.NODE_ENV === 'development') {
              console.log('🔄 功能选项变化:', {
                isSelectAll,
                isCascade,
                values,
              });
            }

            // 处理全选逻辑（单一数据源 + 直接调用Tree组件方法）
            if (isSelectAll) {
              // 更新数据管理器（单一数据源）
              dataManager.toggleSelectAllCircuits(true);

              // 直接调用Tree组件方法确保视图更新
              setTimeout(() => {
                const treeComponentRef =
                  formApi?.getFieldComponentRef<any>('circuitTree');
                if (treeComponentRef?.setCheckedKeys) {
                  // 使用原始数据获取所有节点ID（包括父节点）
                  const allNodeIds = dataManager.getAllNodeIds(
                    dataManager.circuitTree.value,
                  );
                  treeComponentRef.setCheckedKeys(allNodeIds);
                  if (process.env.NODE_ENV === 'development') {
                    console.log(
                      '🔄 全选设置完成:',
                      allNodeIds.length,
                      '个节点（包括父节点）',
                    );
                  }
                }
              }, 50);
            } else {
              // 清空选中状态（单一数据源）
              dataManager.clearCircuitSelection();

              // 直接调用Tree组件方法确保视图更新
              setTimeout(() => {
                const treeComponentRef =
                  formApi?.getFieldComponentRef<any>('circuitTree');
                if (treeComponentRef?.setCheckedKeys) {
                  treeComponentRef.setCheckedKeys([]);
                  if (process.env.NODE_ENV === 'development') {
                    console.log('🔄 清空选中完成');
                  }
                }
              }, 50);
            }
          },
        },
      },

      // 简化的树形组件（回到基本配置）
      {
        component: 'Tree',
        fieldName: 'circuitTree',
        componentProps: {
          // 使用原始数据，不通过数据管理器筛选
          data: computed(() => dataManager.circuitTree.value),
          showCheckbox: true,
          nodeKey: 'id',
          defaultExpandAll: false,
          // 修复的级联选择逻辑
          checkStrictly: computed(() => {
            const isCascade = checkboxGroupValue.value.includes('cascade');
            if (process.env.NODE_ENV === 'development') {
              console.log('🔄 级联选择状态:', {
                isCascade,
                checkStrictly: !isCascade,
              });
            }
            return !isCascade; // checkStrictly 为 true 表示不级联，为 false 表示级联
          }),
          // Tree组件内部筛选方法
          filterNodeMethod: (value: string, data: any) => {
            if (!value) return true;
            const keyword = value.toLowerCase();
            // 筛选节点名称包含关键字的节点
            return data.label?.toLowerCase().includes(keyword) || false;
          },
          expandedKeys: computed(() => dataManager.expandedCircuitIds.value),
          props: {
            label: 'label',
            children: 'children',
          },
          // 回路选择变化时更新数据管理器状态（通过watch监听自动更新表格）
          onCheck: (checkedNodes: any, checkedInfo: any) => {
            dataManager.handleCircuitNodeCheck(checkedNodes, checkedInfo);
            if (process.env.NODE_ENV === 'development') {
              console.log('🔄 回路选择变化，数据管理器已更新');
            }
          },
          onNodeExpand: (nodeData: any) => {
            dataManager.handleCircuitNodeExpand(nodeData);
          },
          onNodeCollapse: (nodeData: any) => {
            dataManager.handleCircuitNodeCollapse(nodeData);
          },
        },
        // 移除复杂的 dependencies，使用简化的响应式配置
      },
    ],
    // 不显示默认的操作按钮（查询、重置等）
    showDefaultActions: false,
    // 控制表单是否显示折叠按钮
    showCollapseButton: false,
    // 是否在字段值改变时提交表单
    submitOnChange: true,
    // 按下回车时是否提交表单
    submitOnEnter: false,
    // 表单布局
    layout: 'vertical',
    // 使用自定义类名，通过 CSS 覆盖实现 flexbox 布局
    wrapperClass: 'grid-cols-1',
  });

  // 移除了复杂的辅助函数，使用数据管理器中的方法

  return [Form, formApi] as const;
}

/**
 * 导出简化版本的创建函数
 */
export { createSimplifiedAsideForm as createAsideForm };
