import { requestClient } from '#/api/request';

/**
 * 逐日极值数据类型定义
 */
export interface PowerExtremeData {
  circuitName: string; // 回路名称
  date: number; // 日期（时间戳）
  // 总有功功率(kW)
  totalPowerMaxValue: number; // 最大值数值
  totalPowerMaxTime: number; // 最大值发生时间戳
  totalPowerMinValue: number; // 最小值数值
  totalPowerMinTime: number; // 最小值发生时间戳
  totalPowerAvgValue: number; // 平均值
}

/**
 * 逐日极值查询参数（业务参数）
 */
export interface PowerExtremeQueryParams {
  startTime?: string; // 开始时间
  endTime?: string; // 结束时间
  reportType?: 'extremeData'; // 报表类型
  electricalCategory?: string; // 电力类别 (1:功率, 2:电流, 3:相电压, 4:线电压, 5:不平衡度, 6:电压谐波, 7:电流谐波)
  // 右侧表单的checkbox参数（逐日极值只有一个，0:不勾选, 1:勾选）
  showTotalPower?: number; // 显示总有功功率
  // 左侧筛选表单的参数
  enterpriseId?: string; // 企业ID
  energyType?: string; // 能源类型 (1:电, 2:气, 3:水等)
  siteId?: string; // 站点ID
  keyword?: string; // 关键字
  circuitNames?: string[]; // 选中的回路ID列表（复数形式）
}

/**
 * 分页参数
 */
export interface PaginationParams {
  page: number; // 页码
  pageSize: number; // 每页数量
}

/**
 * 完整的请求参数结构
 */
export interface PowerExtremeRequestParams {
  query: PowerExtremeQueryParams; // 查询参数
  pagination: PaginationParams; // 分页参数
}

/**
 * 逐日极值响应数据
 */
export interface PowerExtremeResponse {
  items: PowerExtremeData[];
  total: number;
  page: number;
  pageSize: number;
  reportType?: string;
}

/**
 * 获取逐日极值数据（POST方法）
 * @param requestParams 请求参数（包含query和pagination）
 * @returns 逐日极值数据
 */
export async function getPowerExtremesDailyApi(
  requestParams: PowerExtremeRequestParams,
): Promise<PowerExtremeResponse> {
  return requestClient.post('/power/extremes-daily', requestParams);
}
