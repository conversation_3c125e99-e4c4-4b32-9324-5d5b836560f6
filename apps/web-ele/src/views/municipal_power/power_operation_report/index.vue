<script setup lang="ts">
import type { ReportType } from './data';

import type { VxeTableGridOptions } from '#/adapter/vxe-table';

import { onMounted, ref, watch } from 'vue';

import { Page } from '@vben/common-ui';
import { IconifyIcon } from '@vben/icons';

import dayjs from 'dayjs';
import { ElButton } from 'element-plus';

import { useVbenForm } from '#/adapter/form';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getPowerOperationReportApi } from '#/api/power/operation-report';
import { useAsideFormData } from '#/composables/useAsideFormData';

import { useColumns } from './data';
import { createFormOptions } from './form-config';
import { createSimplifiedAsideForm } from './simplified-aside-form-config';

defineOptions({
  name: 'PowerOperationReport',
});

/* .左侧过滤筛选 - 使用集中式数据管理 */

// 创建数据管理器
const dataManager = useAsideFormData();

// 创建表单实例（在数据管理器创建后立即创建，但数据会异步加载）
const [asideForm, asideFormApi] = createSimplifiedAsideForm(dataManager);

// 防抖请求的状态管理
const requestDebounceMap = new Map<string, number>();

// 调试函数：手动检查数据管理器状态
const debugDataManager = () => {
  console.log('=== 数据管理器调试信息 ===');
  console.log('企业数据:', dataManager.enterprises.value);
  console.log('站点数据:', dataManager.sites.value);
  console.log('选中的企业ID:', dataManager.selectedEnterpriseId.value);
  console.log('选中的站点ID:', dataManager.selectedSiteId.value);
  console.log('加载状态:', dataManager.loading.value);
  console.log('初始化状态:', dataManager.initialized.value);
  console.log('企业选项:', dataManager.enterpriseOptions.value);
  console.log('站点选项:', dataManager.siteOptions.value);
  console.log('========================');
};

// 全局暴露，方便调试
if (process.env.NODE_ENV === 'development') {
  (window as any).asideFormApi = asideFormApi;
  (window as any).dataManager = dataManager;
  (window as any).debugDataManager = debugDataManager;
}

// 防抖的表格数据获取函数
function debouncedFetchTableData(reportType: ReportType, delay = 300) {
  const key = `fetch_${reportType}`;

  // 清除之前的定时器
  if (requestDebounceMap.has(key)) {
    clearTimeout(requestDebounceMap.get(key));
  }

  // 设置新的定时器
  const timerId = setTimeout(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('🚀 防抖后执行表格数据获取:', reportType);
    }
    fetchTableData(reportType);
    requestDebounceMap.delete(key);
  }, delay);

  requestDebounceMap.set(key, timerId);
}

// 同步数据管理器的值到表单
const syncFormValues = async () => {
  if (asideFormApi) {
    const enterpriseId = dataManager.selectedEnterpriseId.value;
    const siteId = dataManager.selectedSiteId.value;

    asideFormApi.setFieldValue('enterpriseId', enterpriseId);
    asideFormApi.setFieldValue('siteId', siteId);

    if (process.env.NODE_ENV === 'development') {
      console.log('🔄 同步表单值:', {
        enterpriseId: enterpriseId,
        siteId: siteId,
      });

      // 验证表单值是否正确设置
      setTimeout(async () => {
        const formValues = await asideFormApi.getValues();
        console.log('🔍 表单值验证:', formValues);
      }, 100);
    }
  }
};

// 当前激活的标签页
const activeTab = ref('dayly');

// 查询参数存储
const daylyQueryParams = ref({});

// 处理查询参数的工具函数
function getQueryParams(reportType: ReportType, formData: any) {
  // 获取时间间隔，默认为一分钟
  const timeInterval = formData.timeInterval || '1';

  if (reportType === 'dayly') {
    const date = formData.date || dayjs().format('YYYY-MM-DD');
    return {
      startTime: date,
      endTime: date,
      timeInterval,
    };
  }

  return { startTime: '', endTime: '', timeInterval };
}

// 创建基础表格配置（不设置初始数据，通过 API 动态设置）
const createGridOptions = (reportType: ReportType) => {
  return {
    columns: useColumns(reportType),
    height: 'auto',
    keepSource: true,
    showFooter: false,
    // 不设置 loading 和 data，通过 API 动态设置
    // 表格自适应配置
    autoResize: true,
    columnConfig: {
      resizable: true,
      useKey: true,
      isCurrent: true,
    },
    // 列自适应配置
    fit: true, // 列宽自适应
    autoWidth: true, // 自动计算列宽
    // 表格布局配置
    border: true,
    stripe: true,
    // 滚动配置 - 关键配置！
    showOverflow: true, // 显示溢出内容，启用水平滚动
    pagerConfig: {
      enabled: false,
    },
    rowConfig: {
      keyField: 'date',
    },
  } as VxeTableGridOptions<any>;
};

/**
 * 获取左侧筛选表单的值（使用数据管理器作为数据源）
 */
async function getAsideFormValues() {
  try {
    // 获取表单中的其他值（如能源类型、关键字等）
    const formValues = asideFormApi ? await asideFormApi.getValues() : {};

    // 企业和站点数据从数据管理器获取（保持数据源一致性）
    const result = {
      enterpriseId: dataManager.selectedEnterpriseId.value, // 从数据管理器获取
      energyType: formValues.powerType || '1', // 能源类型（电、气、水等）
      siteId: dataManager.selectedSiteId.value, // 从数据管理器获取
      keyword: formValues.circuitFilter || '', // 筛选关键字
      circuitNames: dataManager.selectedCircuitNames.value, // 从数据管理器获取选中的回路名称
    };

    if (process.env.NODE_ENV === 'development') {
      console.log('📋 获取筛选表单值:', result);
      console.log('📋 数据管理器状态:', {
        selectedEnterpriseId: dataManager.selectedEnterpriseId.value,
        selectedSiteId: dataManager.selectedSiteId.value,
        enterprisesCount: dataManager.enterprises.value.length,
        sitesCount: dataManager.sites.value.length,
      });
      console.log('📋 表单原始值:', formValues);

      // 检查关键字段是否为空
      if (!result.enterpriseId) {
        console.warn(
          '⚠️ 企业ID为空！数据管理器值:',
          dataManager.selectedEnterpriseId.value,
        );
      }
      if (!result.siteId) {
        console.warn(
          '⚠️ 站点ID为空！数据管理器值:',
          dataManager.selectedSiteId.value,
        );
      }
    }

    return result;
  } catch (error) {
    console.warn('获取左侧筛选表单值失败，使用默认空值:', error);
    return {
      enterpriseId: '',
      energyType: '1',
      siteId: '',
      keyword: '',
      circuitNames: [],
    };
  }
}

/**
 * 合并左侧筛选表单值到右侧表单提交数据（异步方法）
 */
async function mergeAsideFormValues(rightFormValues: any) {
  const asideValues = await getAsideFormValues();
  const mergedValues = {
    ...rightFormValues,
    ...asideValues,
  };

  return mergedValues;
}

// 表单提交处理函数（异步）
async function handleDaylySubmit(values: any) {
  const mergedValues = await mergeAsideFormValues(values);
  daylyQueryParams.value = mergedValues;
  fetchTableData('dayly');
}

// 创建表单组件（获取表单 API 引用）
const [DaylyQueryForm, daylyFormApi] = useVbenForm({
  ...createFormOptions('dayly'),
  handleSubmit: handleDaylySubmit,
});

// 创建表格组件
const [DaylyGrid, daylyGridApi] = useVbenVxeGrid({
  gridOptions: createGridOptions('dayly'),
});

// 数据查询函数
const fetchTableData = async (reportType: ReportType) => {
  // 在获取表单值之前，强制同步数据管理器的值到表单
  if (
    dataManager.selectedEnterpriseId.value &&
    dataManager.selectedSiteId.value
  ) {
    await syncFormValues();
    // 等待一下确保同步完成
    await new Promise((resolve) => setTimeout(resolve, 100));
  }

  // 获取对应的 GridApi 和表单数据
  let gridApi: any;
  let formData: any;

  switch (reportType) {
    case 'dayly': {
      gridApi = daylyGridApi;
      // 合并左侧表单值和查询参数
      const asideFormValues = await getAsideFormValues();
      formData = { ...daylyQueryParams.value, ...asideFormValues };

      break;
    }
    // No default
  }

  if (!gridApi) {
    console.error(`GridApi for ${reportType} not found`);
    return;
  }

  // 设置加载状态
  gridApi.setGridOptions({ loading: true });

  try {
    // 处理查询参数（包含时间和时间间隔）
    const { startTime, endTime, timeInterval } = getQueryParams(
      reportType,
      formData,
    );

    // 构建新的请求参数结构：分离查询参数和分页参数
    const requestParams = {
      query: {
        // 时间和时间间隔参数
        startTime,
        endTime,
        timeInterval,
        reportType,
        // 左侧筛选表单的值（使用新的字段名）
        enterpriseId: formData.enterpriseId || '',
        energyType: formData.energyType || '1',
        siteId: formData.siteId || '',
        keyword: formData.keyword || '',
        circuitNames: formData.circuitNames || [],
        // 注意：timeInterval 使用右侧表单的值（优先级更高）
      },
      pagination: {
        page: 1,
        pageSize: 50,
      },
    };

    if (process.env.NODE_ENV === 'development') {
      console.log('🚀 发送API请求参数:', requestParams);
      console.log('📋 左侧表单数据:', formData);
    }

    const response = await getPowerOperationReportApi(requestParams);

    // 通过 GridApi 设置表格数据
    const tableData = response.items || [];
    gridApi.setGridOptions({
      data: tableData,
      loading: false,
    });
  } catch (error) {
    console.error(`Failed to fetch ${reportType} data:`, error);
    // 设置空数据和关闭加载状态
    gridApi.setGridOptions({
      data: [],
      loading: false,
    });
  }
};

// 日期切换处理函数
function handleDateChange(direction: 'next' | 'prev') {
  const currentDate = dayjs(
    daylyQueryParams.value.date || dayjs().format('YYYY-MM-DD'),
  );
  const newDate =
    direction === 'prev'
      ? currentDate.subtract(1, 'day')
      : currentDate.add(1, 'day');

  // 简单的边界检查：不允许选择未来日期
  if (direction === 'next' && newDate.isAfter(dayjs(), 'day')) {
    console.log('不能选择未来日期');
    return;
  }

  const newDateStr = newDate.format('YYYY-MM-DD');
  console.log(`切换日期: ${currentDate.format('YYYY-MM-DD')} -> ${newDateStr}`);

  // 更新日报查询参数
  daylyQueryParams.value = {
    ...daylyQueryParams.value,
    date: newDateStr,
  };

  // 同步更新表单字段
  daylyFormApi.setFieldValue('date', newDateStr);

  // 重新查询数据
  fetchTableData('dayly');
}

// 页面初始化时加载数据（新版本 - 使用集中式数据管理）
onMounted(async () => {
  try {
    if (process.env.NODE_ENV === 'development') {
      console.log('🚀 页面初始化开始...');
    }

    // 1. 初始化筛选表单数据（企业和站点）
    await dataManager.initializeData({
      // 可以从路由参数或用户偏好中获取默认值
      // defaultEnterpriseId: route.query.enterprise as string,
      // defaultSiteId: route.query.site as string,
    });

    // 2. 同步数据管理器的值到表单（延迟执行，确保表单已完全初始化）
    setTimeout(() => {
      syncFormValues();
    }, 200);

    // 3. 验证数据管理器状态
    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 初始化完成后的数据管理器状态:', {
        selectedEnterpriseId: dataManager.selectedEnterpriseId.value,
        selectedSiteId: dataManager.selectedSiteId.value,
        enterprisesCount: dataManager.enterprises.value.length,
        sitesCount: dataManager.sites.value.length,
        initialized: dataManager.initialized.value,
      });
    }

    // 2. 初始化默认查询参数
    daylyQueryParams.value = {
      date: dayjs().format('YYYY-MM-DD'),
      timeInterval: '1', // 默认选择一分钟
    };

    // 3. 数据准备完成后，只加载当前标签页的数据（避免不必要的请求）
    debouncedFetchTableData(activeTab.value, 500); // 初始化使用较长的延迟，确保所有数据都准备好

    if (process.env.NODE_ENV === 'development') {
      console.log('✅ 页面初始化完成');

      // 在所有函数定义完成后暴露到全局（仅开发环境）
      (window as any).debugDataManager = debugDataManager;
      (window as any).getAsideFormValues = getAsideFormValues;
      (window as any).syncFormValues = syncFormValues;
      (window as any).dataManager = dataManager;
      (window as any).fetchTableData = fetchTableData;

      console.log('🔧 调试函数已暴露到全局:', {
        debugDataManager: '检查数据管理器状态',
        getAsideFormValues: '获取表单值',
        syncFormValues: '同步表单值',
        dataManager: '数据管理器实例',
        fetchTableData: '获取表格数据',
      });
    }
  } catch (error) {
    console.error('❌ 页面初始化失败:', error);
  }
});

// 监听企业、站点和回路变化，自动刷新表格数据（合并监听，避免重复请求）
watch(
  [
    () => dataManager.selectedEnterpriseId.value,
    () => dataManager.selectedSiteId.value,
    () => dataManager.checkedCircuitIds.value.length, // 监听回路选择变化
  ],
  (
    [newEnterpriseId, newSiteId, newCircuitCount],
    [oldEnterpriseId, oldSiteId, oldCircuitCount],
  ) => {
    // 只有在数据初始化完成后才触发刷新
    if (newEnterpriseId && newSiteId) {
      // 检查是否真的有变化
      const enterpriseChanged = newEnterpriseId !== oldEnterpriseId;
      const siteChanged = newSiteId !== oldSiteId;
      const circuitChanged = newCircuitCount !== oldCircuitCount;

      if (enterpriseChanged || siteChanged || circuitChanged) {
        if (process.env.NODE_ENV === 'development') {
          console.log('🔄 筛选条件变化，刷新表格数据:', {
            enterprise: newEnterpriseId,
            site: newSiteId,
            circuitCount: newCircuitCount,
            enterpriseChanged,
            siteChanged,
            circuitChanged,
          });
        }
        // 使用防抖函数刷新当前标签页的数据
        debouncedFetchTableData(activeTab.value);
      }
    }
  },
  {
    // 不在初始化时立即执行
    immediate: false,
  },
);

// 监听数据管理器的值变化，自动同步到表单
watch(
  [
    () => dataManager.selectedEnterpriseId.value,
    () => dataManager.selectedSiteId.value,
  ],
  ([newEnterpriseId, newSiteId]) => {
    if (process.env.NODE_ENV === 'development') {
      console.log('🔄 数据管理器值变化，同步到表单:', {
        enterpriseId: newEnterpriseId,
        siteId: newSiteId,
      });
    }
    syncFormValues();
  },
  { immediate: false },
);
</script>

<template>
  <Page auto-content-height>
    <div class="flex h-full gap-4">
      <div
        class="aside bg-card border-border h-full w-[300px] shrink-0 border py-4"
      >
        <asideForm />
      </div>
      <div class="h-full w-[calc(100%-324px)] flex flex-col gap-4">
        <div class="px-2 bg-card border-border">
          <DaylyQueryForm>
            <template #reset-before>
              <ElButton
                class="[html[data-theme='tech-blue'].dark_&]:bg-accent-foreground"
                type="primary"
                @click="() => handleDateChange('prev')"
              >
                <IconifyIcon icon="lucide:chevron-left" class="h-4 w-4" />
                上一日
              </ElButton>
              <ElButton
                class="[html[data-theme='tech-blue'].dark_&]:bg-accent-foreground"
                type="primary"
                @click="() => handleDateChange('next')"
              >
                下一日
                <IconifyIcon icon="lucide:chevron-right" class="h-4 w-4" />
              </ElButton>
            </template>
          </DaylyQueryForm>
        </div>
        <DaylyGrid class="flex-1 min-h-0" />
      </div>
    </div>
  </Page>
</template>
<style scoped lang="scss">
// 响应式设计
@media (max-width: 768px) {
  :deep(.vxe-table) {
    font-size: 12px;
  }
}
:deep(.el-tree) {
  height: 50vh !important;
  overflow-y: auto;
  background: transparent;
}
:deep(.aside form > .grid > div.pb-6) {
  padding-bottom: 20px !important;
}
:deep(.aside form > .grid > div.pb-6:nth-child(4)),
:deep(.aside form > .grid > div.pb-6:nth-child(5)) {
  padding-bottom: 0px !important;
}

// 强制覆盖 Vben Form 的硬编码 grid 布局
:deep(.compact-form-layout) {
  // 使用 !important 强制覆盖硬编码的 grid 类
  display: flex !important;
  flex-wrap: wrap !important;
  align-items: center !important;
  gap: 1rem 2.5rem !important;
  justify-content: flex-start !important;

  // 大屏幕不换行，表单项水平排列
  @media (min-width: 1200px) {
    flex-wrap: nowrap !important;
    gap: 1rem 3rem !important;
  }

  // 中等屏幕适当换行
  @media (max-width: 1199px) and (min-width: 768px) {
    gap: 1rem 2rem !important;
  }

  // 小屏幕垂直排列
  @media (max-width: 767px) {
    flex-direction: column !important;
    align-items: stretch !important;
    gap: 1rem !important;
  }

  // 表单项容器样式
  > * {
    flex: 0 0 auto !important;
    margin-bottom: 0 !important;
    padding: 12px 0  !important;
    // 小屏幕下占满宽度
    @media (max-width: 767px) {
      flex: 1 1 100% !important;
      width: 100% !important;
    }
  }

  // 操作按钮区域特殊处理 - 紧贴表单项，空间不够时自动换行
  > *:last-child {
    flex: 0 1 auto !important; // 允许收缩，空间不够时换行
    width: auto !important; // 覆盖 w-full 的 width: 100%
    min-width: fit-content !important; // 最小宽度为内容宽度

    // 强制覆盖组件内联样式 - 使用更高优先级
    grid-column: unset !important; // 覆盖 col-span-full 和内联样式
    margin-left: 0 !important; // 覆盖内联样式的 margin-left: auto

    // 针对内部的 form-actions 组件
    &.w-full {
      width: auto !important; // 覆盖组件自身的 w-full
    }

    &.col-span-full {
      grid-column: unset !important; // 覆盖 col-span-full 类
    }

    &.text-right {
      text-align: left !important; // 覆盖右对齐
    }

    .w-full {
      width: auto !important; // 覆盖组件内部的 w-full
    }

    @media (max-width: 767px) {
      flex: 1 1 100% !important; // 小屏幕下独占一行
      width: 100% !important; // 小屏幕下恢复全宽
      grid-column: 1 / -1 !important; // 小屏幕下恢复全列
      margin-left: auto !important; // 小屏幕下恢复右对齐

      &.w-full {
        width: 100% !important;
      }

      &.text-right {
        text-align: right !important; // 小屏幕下恢复右对齐
      }

      .w-full {
        width: 100% !important;
      }
    }
  }

  // 确保表单控件保持合适的宽度
  .el-select,
  .el-date-editor,
  .el-input,
  .el-cascader {
    min-width: 180px !important; // 设置最小宽度
    width: auto !important;
  }

  // 日期范围选择器需要更大的宽度
  .el-date-editor--daterange,
  .el-date-editor--datetimerange {
    min-width: 280px !important;
  }

  // 下拉选择器特殊处理
  .el-select {
    .el-input {
      min-width: 180px !important;
    }
  }
}

// 更强的选择器，确保覆盖硬编码的 grid 类
:deep(div.compact-form-layout.grid) {
  display: flex !important;
  flex-wrap: wrap !important;
  align-items: center !important;
  gap: 1rem 2.5rem !important;
  justify-content: flex-start !important;
}
</style>
