<script lang="ts" setup>
import { onMounted, ref, watch } from 'vue';
import { ElSelect, ElOption, ElDatePicker } from 'element-plus';

import {
  EchartsUI,
  type EchartsUIType,
  useEcharts,
} from '@vben/plugins/echarts';

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);

// 控制选项
const selectedMetric = ref('current'); // 相电流、电流、安全用电
const selectedDate = ref(new Date());

// 指标选项
const metricOptions = [
  { label: '相电流', value: 'current' },
  { label: '电流', value: 'total_current' },
  { label: '安全用电', value: 'safety' },
];

// 生成24小时的时间标签
const generateTimeLabels = () => {
  const labels = [];
  for (let i = 0; i < 24; i++) {
    labels.push(`${i.toString().padStart(2, '0')}:00`);
  }
  return labels;
};

// 生成模拟的三相电流数据 - 参考图片中的波动特征
const generateCurrentData = (baseValue: number, variance: number) => {
  const data = [];
  for (let i = 0; i < 24; i++) {
    // 模拟一天中的电流变化，白天用电量较高，夜间较低
    const timeMultiplier =
      Math.sin((i / 24) * Math.PI * 2 + Math.PI / 2) * 0.3 + 0.7;
    // 添加随机波动
    const randomVariance = (Math.random() - 0.5) * variance;
    const value = baseValue * timeMultiplier + randomVariance;
    data.push(Math.max(0, Number(value.toFixed(2))));
  }
  return data;
};

// 获取当前指标的数据配置
const getMetricConfig = () => {
  const timeLabels = generateTimeLabels();

  switch (selectedMetric.value) {
    case 'current':
      return {
        legendData: ['A相电流', 'B相电流', 'C相电流'],
        series: [
          {
            name: 'A相电流',
            data: generateCurrentData(0.8, 0.3),
            color: '#ff6b35', // 橙红色
          },
          {
            name: 'B相电流',
            data: generateCurrentData(0.7, 0.25),
            color: '#4ecdc4', // 青色
          },
          {
            name: 'C相电流',
            data: generateCurrentData(1.8, 0.4),
            color: '#45b7d1', // 蓝色
          },
        ],
        unit: 'A',
        yAxisMax: 3,
      };
    case 'total_current':
      return {
        legendData: ['总电流'],
        series: [
          {
            name: '总电流',
            data: generateCurrentData(3.3, 0.5), // 三相电流之和
            color: '#10b981', // 绿色
          },
        ],
        unit: 'A',
        yAxisMax: 5,
      };
    case 'safety':
      return {
        legendData: ['安全指数'],
        series: [
          {
            name: '安全指数',
            data: generateCurrentData(85, 10), // 安全指数 75-95
            color: '#f59e0b', // 黄色
          },
        ],
        unit: '%',
        yAxisMax: 100,
      };
    default:
      return {
        legendData: ['A相电流', 'B相电流', 'C相电流'],
        series: [
          {
            name: 'A相电流',
            data: generateCurrentData(0.8, 0.3),
            color: '#ff6b35',
          },
          {
            name: 'B相电流',
            data: generateCurrentData(0.7, 0.25),
            color: '#4ecdc4',
          },
          {
            name: 'C相电流',
            data: generateCurrentData(1.8, 0.4),
            color: '#45b7d1',
          },
        ],
        unit: 'A',
        yAxisMax: 3,
      };
  }
};

// 渲染图表
const renderChart = () => {
  const timeLabels = generateTimeLabels();
  const config = getMetricConfig();

  renderEcharts({
    toolbox: {
      feature: {
        saveAsImage: {
          title: '保存为图片',
        },
        dataZoom: {
          title: {
            zoom: '区域缩放',
            back: '区域缩放还原',
          },
        },
        restore: {
          title: '还原',
        },
        dataView: {
          title: '数据视图',
          readOnly: false,
        },
      },
      right: '3%',
      top: '8%',
    },
    grid: {
      bottom: '10%',
      containLabel: true,
      left: '3%',
      right: '3%',
      top: '25%',
    },
    legend: {
      top: '8%',
      data: config.legendData,
      textStyle: {
        fontSize: 11,
      },
    },
    series: config.series.map((item) => ({
      name: item.name,
      data: item.data,
      type: 'line',
      smooth: true,
      lineStyle: {
        color: item.color,
        width: 2,
      },
      itemStyle: {
        color: item.color,
      },
      symbol: 'circle',
      symbolSize: 3,
    })),
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        lineStyle: {
          color: '#666',
          width: 1,
          type: 'dashed',
        },
      },
      formatter: function (params: any) {
        let result = `时间: ${params[0].axisValue}<br/>`;
        params.forEach((param: any) => {
          result += `${param.marker}${param.seriesName}: ${param.value} ${config.unit}<br/>`;
        });
        return result;
      },
    },
    xAxis: {
      axisTick: {
        show: false,
      },
      boundaryGap: false,
      data: timeLabels,
      type: 'category',
      axisLabel: {
        fontSize: 11,
        interval: 2, // 每隔2个小时显示一个标签
      },
    },
    yAxis: {
      axisTick: {
        show: false,
      },
      type: 'value',
      axisLine: {
        show: false,
      },
      axisLabel: {
        fontSize: 11,
        formatter: `{value} ${config.unit}`,
      },
      splitLine: {
        lineStyle: {
          color: '#f3f4f6',
          type: 'dashed',
        },
      },
      min: 0,
      max: config.yAxisMax,
    },
  });
};

// 监听选项变化，重新渲染图表
watch([selectedMetric, selectedDate], () => {
  renderChart();
});

onMounted(() => {
  renderChart();
});
</script>

<template>
  <div class="flex h-full flex-col">
    <!-- 控制面板 -->
    <div class="mb-4 flex items-center gap-4 p-3">
      <!-- 指标选择 -->
      <div class="flex items-center gap-2">
        <ElSelect v-model="selectedMetric" placeholder="选择指标">
          <ElOption
            v-for="option in metricOptions"
            :key="option.value"
            :label="option.label"
            :value="option.value"
          />
        </ElSelect>
      </div>

      <!-- 日期选择 -->
      <div class="flex items-center gap-2">
        <ElDatePicker
          v-model="selectedDate"
          type="date"
          placeholder="选择日期"
        />
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="min-h-0 flex-1">
      <EchartsUI ref="chartRef" height="100%" />
    </div>
  </div>
</template>
