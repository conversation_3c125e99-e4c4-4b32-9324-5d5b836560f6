<template>
  <Page auto-content-height>
    <div
      class="flex min-h-0 flex-col gap-4 overflow-hidden sm:h-auto md:h-auto lg:h-full"
    >
      <!-- 顶部工具栏 -->
      <div
        class="bg-card border-border flex min-h-[45px] items-center gap-4 rounded-lg border px-4 py-2 lg:h-[45px] lg:py-0"
      >
        <!-- 左侧返回按钮和标题 -->
        <div class="flex items-center gap-3">
          <ElButton type="default" size="default" @click="goBack">
            <IconifyIcon icon="lucide:arrow-left" class="mr-1 h-4 w-4" />
            返回
          </ElButton>
          <div class="text-foreground font-medium">设备监控详情</div>
          <div v-if="deviceInfo" class="text-muted-foreground text-sm">
            {{ deviceInfo.name }} ({{ deviceInfo.id }})
          </div>
        </div>

        <!-- 右侧操作按钮 -->
        <div class="ml-auto flex shrink-0 gap-2">
          <ElButton type="default" size="default" @click="refreshData">
            <IconifyIcon icon="lucide:refresh-cw" class="mr-1 h-4 w-4" />
            刷新
          </ElButton>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div
        class="flex flex-1 gap-4 overflow-y-auto sm:flex-col md:flex-col lg:flex-row lg:overflow-hidden"
      >
        <!-- 左侧设备信息卡片 -->
        <div class="w-full shrink-0 sm:w-full md:w-full lg:w-80">
          <AnalysisChartCard title="设备信息" class="h-auto max-h-[200px] sm:max-h-[240px] md:max-h-[280px] lg:h-full lg:max-h-none">
            <div v-if="deviceInfo" class="flex flex-col gap-2 overflow-y-auto sm:gap-3 lg:h-full lg:gap-4 lg:overflow-y-visible">
              <!-- 设备头像和基本信息 -->
              <div class="bg-muted/30 flex items-center gap-3 rounded-lg p-2 sm:gap-4 sm:p-3 lg:p-4">
                <div
                  class="bg-primary/10 flex h-12 w-12 items-center justify-center rounded-full sm:h-14 sm:w-14 lg:h-16 lg:w-16"
                >
                  <IconifyIcon
                    :icon="getDeviceIcon(deviceInfo.type)"
                    class="text-primary h-6 w-6 sm:h-7 sm:w-7 lg:h-8 lg:w-8"
                  />
                </div>
                <div class="flex-1">
                  <div class="text-foreground font-medium">
                    {{ deviceInfo.name }}
                  </div>
                  <div class="text-muted-foreground text-sm">
                    {{ deviceInfo.id }}
                  </div>
                  <div class="mt-1">
                    <span
                      class="inline-flex items-center rounded-full px-2 py-1 text-xs font-medium"
                      :class="getStatusClass(deviceInfo.status)"
                    >
                      <span
                        class="mr-1 h-2 w-2 rounded-full"
                        :class="getStatusDotClass(deviceInfo.status)"
                      ></span>
                      {{ getStatusText(deviceInfo.status) }}
                    </span>
                  </div>
                </div>
              </div>

              <!-- 设备详细信息 -->
              <div class="space-y-2">
                <div class="flex justify-between text-sm">
                  <span class="text-muted-foreground">设备类型:</span>
                  <span class="text-foreground">{{
                    getDeviceTypeText(deviceInfo.type)
                  }}</span>
                </div>
                <div class="flex justify-between text-sm">
                  <span class="text-muted-foreground">设备地址:</span>
                  <span class="text-foreground">{{ deviceInfo.address }}</span>
                </div>
                <div class="flex justify-between text-sm">
                  <span class="text-muted-foreground">安装位置:</span>
                  <span class="text-foreground">{{ deviceInfo.location }}</span>
                </div>
                <div class="flex justify-between text-sm">
                  <span class="text-muted-foreground">最后更新:</span>
                  <span class="text-foreground">{{
                    deviceInfo.lastUpdate
                  }}</span>
                </div>
              </div>

              <!-- 实时数据 -->
              <div class="space-y-2 sm:space-y-3">
                <div class="text-sm font-medium">实时数据</div>
                <div class="grid grid-cols-2 gap-2 sm:gap-3">
                  <div
                    v-if="deviceInfo.values.voltage"
                    class="bg-muted/20 rounded-lg p-1 text-center sm:p-2"
                  >
                    <div class="text-muted-foreground text-xs">电压</div>
                    <div class="text-sm font-medium">
                      {{ deviceInfo.values.voltage?.toFixed(2) || '0.00' }}V
                    </div>
                  </div>
                  <div
                    v-if="deviceInfo.values.current"
                    class="bg-muted/20 rounded-lg p-1 text-center sm:p-2"
                  >
                    <div class="text-muted-foreground text-xs">电流</div>
                    <div class="text-sm font-medium">
                      {{ deviceInfo.values.current?.toFixed(2) || '0.00' }}A
                    </div>
                  </div>
                  <div
                    v-if="deviceInfo.values.power"
                    class="bg-muted/20 rounded-lg p-1 text-center sm:p-2"
                  >
                    <div class="text-muted-foreground text-xs">功率</div>
                    <div class="text-sm font-medium">
                      {{ deviceInfo.values.power?.toFixed(2) || '0.00' }}kW
                    </div>
                  </div>
                  <div
                    v-if="deviceInfo.values.temperature"
                    class="bg-muted/20 rounded-lg p-1 text-center sm:p-2"
                  >
                    <div class="text-muted-foreground text-xs">温度</div>
                    <div class="text-sm font-medium">
                      {{
                        deviceInfo.values.temperature?.toFixed(2) || '0.00'
                      }}°C
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 加载状态 -->
            <div v-else class="flex h-full items-center justify-center">
              <div class="text-center">
                <IconifyIcon
                  icon="lucide:loader-2"
                  class="text-muted-foreground mx-auto mb-2 h-8 w-8 animate-spin"
                />
                <div class="text-muted-foreground text-sm">
                  加载设备信息中...
                </div>
              </div>
            </div>
          </AnalysisChartCard>
        </div>

        <!-- 右侧内容区域 - 上下两行布局 -->
        <div
          class="flex min-w-0 flex-1 flex-col gap-4 sm:h-auto md:h-auto lg:h-full"
        >
          <!-- 上方第一行：实时数据卡片 + 设备操作卡片 -->
          <div
            class="flex gap-4 sm:min-h-[200px] sm:flex-col-reverse md:min-h-[250px] md:flex-row lg:min-h-0 lg:flex-[2] lg:flex-row"
          >
            <!-- 实时数据卡片 - 占3/4空间 -->
            <div class="min-w-0 flex-1 sm:w-full md:flex-[3] lg:flex-[3]">
              <AnalysisChartCard title="实时数据" class="h-full">
                <template #title-actions>
                  <div class="flex items-center gap-2">
                    <span class="text-muted-foreground/70 text-base">
                      数据更新时间: {{ deviceInfo?.lastUpdate || '--' }}
                    </span>
                  </div>
                </template>

                <!-- 数据网格展示 - 参考市电看板样式 -->
                <div
                  v-if="deviceInfo"
                  class="flex h-full justify-start overflow-hidden p-4"
                >
                  <!-- 响应式宽度限制 -->
                  <div
                    class="w-full min-w-0 max-w-[90%] lg:max-w-[85%] xl:max-w-[70%]"
                  >
                    <div
                      class="grid h-full min-h-0 grid-cols-2 gap-3 xl:grid-cols-4"
                    >
                      <!-- 三相电流 -->
                      <div
                        class="border-accent-foreground flex flex-col border"
                      >
                        <div
                          class="bg-accent-foreground flex h-[30%] flex-col items-center justify-center p-2 text-center"
                        >
                          <div
                            class="text-card-foreground dark:text-foreground text-sm font-medium"
                          >
                            三相电流
                          </div>
                        </div>
                        <div
                          class="flex h-[70%] w-full flex-col justify-center space-y-2 overflow-hidden px-3 py-2"
                        >
                          <div class="flex min-w-0 items-center text-sm">
                            <span
                              class="text-muted-foreground mr-2 flex-shrink-0 truncate"
                              >A相电流:</span
                            >
                            <span
                              class="text-foreground truncate text-right font-medium"
                              >{{
                                deviceInfo.values.currentA?.toFixed(1) || '0.8'
                              }}
                              A</span
                            >
                          </div>
                          <div class="flex min-w-0 items-center text-sm">
                            <span
                              class="text-muted-foreground mr-2 flex-shrink-0 truncate"
                              >B相电流:</span
                            >
                            <span
                              class="text-foreground truncate text-right font-medium"
                              >{{
                                deviceInfo.values.currentB?.toFixed(1) || '0.7'
                              }}
                              A</span
                            >
                          </div>
                          <div class="flex min-w-0 items-center text-sm">
                            <span
                              class="text-muted-foreground mr-2 flex-shrink-0 truncate"
                              >C相电流:</span
                            >
                            <span
                              class="text-foreground truncate text-right font-medium"
                              >{{
                                deviceInfo.values.currentC?.toFixed(1) || '1.8'
                              }}
                              A</span
                            >
                          </div>
                        </div>
                      </div>

                      <!-- 电流 -->
                      <div
                        class="border-accent-foreground flex flex-col border"
                      >
                        <div
                          class="bg-accent-foreground flex h-[30%] flex-col items-center justify-center p-2 text-center"
                        >
                          <div
                            class="text-card-foreground dark:text-foreground text-sm font-medium"
                          >
                            电流
                          </div>
                        </div>
                        <div
                          class="flex h-[70%] w-full items-center justify-center overflow-hidden"
                        >
                          <div
                            class="flex w-full min-w-0 items-center px-3 py-2 text-sm"
                          >
                            <span
                              class="text-muted-foreground mr-2 flex-shrink-0 truncate"
                              >电流:</span
                            >
                            <span
                              class="text-foreground truncate text-right font-medium"
                              >{{
                                deviceInfo.values.current?.toFixed(1) || '0.8'
                              }}
                              A</span
                            >
                          </div>
                        </div>
                      </div>

                      <!-- 电功率1 -->
                      <div
                        class="border-accent-foreground flex flex-col border"
                      >
                        <div
                          class="bg-accent-foreground text-accent flex h-[30%] flex-col items-center justify-center p-2 text-center"
                        >
                          <div
                            class="text-card-foreground dark:text-foreground text-sm font-medium"
                          >
                            电功率
                          </div>
                        </div>
                        <div
                          class="flex h-[70%] w-full items-center justify-center overflow-hidden"
                        >
                          <div
                            class="flex w-full min-w-0 items-center px-3 py-2 text-sm"
                          >
                            <span
                              class="text-muted-foreground mr-2 flex-shrink-0 truncate"
                              >电功率:</span
                            >
                            <span
                              class="text-foreground truncate text-right font-medium"
                              >{{
                                deviceInfo.values.power1?.toFixed(0) || '0'
                              }}</span
                            >
                          </div>
                        </div>
                      </div>

                      <!-- 电功率2 -->
                      <div
                        class="border-accent-foreground flex flex-col border"
                      >
                        <div
                          class="bg-accent-foreground text-accent flex h-[30%] flex-col items-center justify-center p-2 text-center"
                        >
                          <div
                            class="text-card-foreground dark:text-foreground text-sm font-medium"
                          >
                            电功率
                          </div>
                        </div>
                        <div
                          class="flex h-[70%] w-full items-center justify-center overflow-hidden"
                        >
                          <div
                            class="flex w-full min-w-0 items-center px-3 py-2 text-sm"
                          >
                            <span
                              class="text-muted-foreground mr-2 flex-shrink-0 truncate"
                              >电功率:</span
                            >
                            <span
                              class="text-foreground truncate text-right font-medium"
                              >{{
                                deviceInfo.values.power2?.toFixed(0) || '0'
                              }}</span
                            >
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 数据加载状态 -->
                <div v-else class="flex h-full items-center justify-center">
                  <div class="text-center">
                    <IconifyIcon
                      icon="lucide:loader-2"
                      class="text-muted-foreground mx-auto mb-2 h-8 w-8 animate-spin"
                    />
                    <div class="text-muted-foreground text-sm">
                      加载数据中...
                    </div>
                  </div>
                </div>
              </AnalysisChartCard>
            </div>

            <!-- 设备操作卡片 - 占1/4空间，位于右侧，小屏幕独占一行 -->
            <div
              class="order-last min-w-0 flex-1 sm:w-full md:flex-[1] lg:flex-[1]"
            >
              <AnalysisChartCard title="设备操作" class="h-full">
                <div
                  class="mx-auto flex h-full w-full max-w-xs flex-col justify-evenly gap-3"
                >
                  <div>
                    <ElButton
                      size="default"
                      type="primary"
                      @click="handleReset"
                      class="!flex !h-10 !min-h-10 !w-full !items-center !justify-start !px-4 !py-2 !text-sm"
                    >
                      <IconifyIcon
                        icon="lucide:rotate-ccw"
                        class="mr-2 h-4 w-4 flex-shrink-0"
                      />
                      <span class="truncate">设备复位</span>
                    </ElButton>
                  </div>
                  <div>
                    <ElButton
                      size="default"
                      @click="handleMute"
                      class="!flex !h-10 !min-h-10 !w-full !items-center !justify-start !px-4 !py-2 !text-sm"
                    >
                      <IconifyIcon
                        icon="lucide:volume-x"
                        class="mr-2 h-4 w-4 flex-shrink-0"
                      />
                      <span class="truncate">报警消音</span>
                    </ElButton>
                  </div>
                  <div>
                    <ElButton
                      size="default"
                      @click="handleSelfTest"
                      class="!flex !h-10 !min-h-10 !w-full !items-center !justify-start !px-4 !py-2 !text-sm"
                    >
                      <IconifyIcon
                        icon="lucide:search-check"
                        class="mr-2 h-4 w-4 flex-shrink-0"
                      />
                      <span class="truncate">设备自检</span>
                    </ElButton>
                  </div>
                </div>
              </AnalysisChartCard>
            </div>
          </div>

          <!-- 下方第二行：图表展示卡片 - 60% 高度 -->
          <div
            class="sm:min-h-[350px] md:min-h-[400px] lg:min-h-0 lg:flex-[3]"
          >
            <AnalysisChartCard title="数据趋势" class="h-full">
              <template #title-actions>
                <!-- 视图切换标签页 - 复制数据监控页面样式 -->
                <div
                  class="bg-muted/30 border-border inline-flex h-9 items-center justify-center rounded-lg border p-1 [html[data-theme=tech-blue].dark_&]:border-blue-400/40 [html[data-theme=tech-blue].dark_&]:bg-blue-900/20"
                >
                  <button
                    v-for="tab in trendTabs"
                    :key="tab.value"
                    :class="[
                      'focus-visible:ring-ring inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
                      activeTrendTab === tab.value
                        ? 'bg-accent-foreground [html[data-theme=tech-blue].dark_&]:bg-accent-foreground shadow-sm [html[data-theme=tech-blue].dark_&]:shadow-blue-500/20'
                        : 'hover:bg-background/60 [html[data-theme=tech-blue].dark_&]:hover:bg-blue-800/30',
                    ]"
                    @click="activeTrendTab = tab.value"
                  >
                    <IconifyIcon :icon="tab.icon" class="mr-1 h-3 w-3" />
                    <span class="hidden sm:inline">{{
                      tab.label
                    }}</span>
                  </button>
                </div>
              </template>

              <!-- 内容区域 -->
              <div class="h-full">
                <!-- 历史曲线视图 -->
                <div v-if="activeTrendTab === 'history'" class="h-full">
                  <CurrentTrendChart />
                </div>

                <!-- 报警信息视图 -->
                <div v-else-if="activeTrendTab === 'alarm'" class="h-full">
                  <div class="flex h-full items-center justify-center">
                    <div class="text-center">
                      <IconifyIcon
                        icon="lucide:alert-triangle"
                        class="text-muted-foreground mx-auto mb-4 h-16 w-16"
                      />
                      <div class="text-muted-foreground mb-2 text-lg font-medium">
                        报警信息
                      </div>
                      <div class="text-muted-foreground text-sm">
                        报警功能开发中，敬请期待
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </AnalysisChartCard>
          </div>
        </div>
      </div>
    </div>
  </Page>
</template>

<script setup lang="ts">
import type { TabOption } from '@vben/types';

import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { AnalysisChartCard, Page } from '@vben/common-ui';
import { ElButton } from 'element-plus';
import { IconifyIcon } from '@vben/icons';
import CurrentTrendChart from './components/CurrentTrendChart.vue';

defineOptions({
  name: 'DataMonitoringDetail',
});

// 路由实例
const route = useRoute();
const router = useRouter();

// 设备信息接口
interface DeviceInfo {
  id: string;
  name: string;
  type: 'transformer' | 'switchgear' | 'meter';
  address: string;
  status: 'online' | 'offline' | 'fault';
  location: string;
  lastUpdate: string;
  values: {
    voltage?: number;
    current?: number;
    power?: number;
    temperature?: number;
    // 三相电流
    currentA?: number;
    currentB?: number;
    currentC?: number;
    // 两个电功率字段
    power1?: number;
    power2?: number;
    // 状态信息
    communicationStatus?: 'normal' | 'fault';
    alarmStatus?: 'none' | 'warning' | 'alarm';
    // 安全状态
    temperatureStatus?: 'normal' | 'high' | 'critical';
    loadStatus?: 'normal' | 'overload';
    insulationStatus?: 'good' | 'poor' | 'fault';
  };
}

// 响应式数据
const deviceInfo = ref<DeviceInfo | null>(null);

// 趋势视图切换标签页配置
const trendTabs = ref<TabOption[]>([
  {
    label: '历史曲线',
    value: 'history',
    icon: 'lucide:trending-up',
  },
  {
    label: '报警信息',
    value: 'alarm',
    icon: 'lucide:alert-triangle',
  },
]);

const activeTrendTab = ref('history');

// 获取设备图标
const getDeviceIcon = (type: string) => {
  const icons = {
    transformer: 'lucide:zap',
    switchgear: 'lucide:power',
    meter: 'lucide:gauge',
  };
  return icons[type as keyof typeof icons] || 'lucide:cpu';
};

// 获取设备类型文本
const getDeviceTypeText = (type: string) => {
  const types = {
    transformer: '变压器',
    switchgear: '开关柜',
    meter: '电表',
  };
  return types[type as keyof typeof types] || '未知设备';
};

// 获取状态样式类
const getStatusClass = (status: string) => {
  const classes = {
    online:
      'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400',
    offline: 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400',
    fault: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400',
  };
  return classes[status as keyof typeof classes] || classes.offline;
};

// 获取状态点样式类
const getStatusDotClass = (status: string) => {
  const classes = {
    online: 'bg-green-500',
    offline: 'bg-gray-500',
    fault: 'bg-red-500',
  };
  return classes[status as keyof typeof classes] || classes.offline;
};

// 获取状态文本
const getStatusText = (status: string) => {
  const texts = {
    online: '在线',
    offline: '离线',
    fault: '故障',
  };
  return texts[status as keyof typeof texts] || '未知';
};

// 获取通信状态文本
const getCommunicationStatusText = (status: string) => {
  const texts = {
    normal: '正常',
    fault: '故障',
  };
  return texts[status as keyof typeof texts] || '未知';
};

// 获取报警状态文本
const getAlarmStatusText = (status: string) => {
  const texts = {
    none: '无报警',
    warning: '警告',
    alarm: '报警',
  };
  return texts[status as keyof typeof texts] || '未知';
};

// 获取温度状态文本
const getTemperatureStatusText = (status: string) => {
  const texts = {
    normal: '正常',
    high: '偏高',
    critical: '过高',
  };
  return texts[status as keyof typeof texts] || '未知';
};

// 获取负载状态文本
const getLoadStatusText = (status: string) => {
  const texts = {
    normal: '正常',
    overload: '过载',
  };
  return texts[status as keyof typeof texts] || '未知';
};

// 获取绝缘状态文本
const getInsulationStatusText = (status: string) => {
  const texts = {
    good: '良好',
    poor: '一般',
    fault: '故障',
  };
  return texts[status as keyof typeof texts] || '未知';
};

// 返回上一页
const goBack = () => {
  router.back();
};

// 刷新数据
const refreshData = () => {
  loadDeviceInfo();
};

// 设备操作按钮事件
const handleReset = () => {
  console.log('设备复位操作');
  // 这里可以添加设备复位的API调用
};

const handleMute = () => {
  console.log('设备消音操作');
  // 这里可以添加设备消音的API调用
};

const handleSelfTest = () => {
  console.log('设备自检操作');
  // 这里可以添加设备自检的API调用
};

// 加载设备信息
const loadDeviceInfo = async () => {
  const deviceId = route.params.id as string;

  try {
    // 模拟API调用
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // 模拟设备数据
    deviceInfo.value = {
      id: deviceId,
      name: `设备-${deviceId}`,
      type: 'transformer',
      address: '*************',
      status: 'online',
      location: '1号变电站-A区',
      lastUpdate: new Date().toLocaleString(),
      values: {
        voltage: 220.5 + (Math.random() - 0.5) * 10, // 220±5V
        current: 0.8 + (Math.random() - 0.5) * 0.2, // 0.8±0.1A (参考图片)
        power: 3.35 + (Math.random() - 0.5) * 1, // 3.35±0.5kW
        temperature: 45.8 + (Math.random() - 0.5) * 10, // 45±5°C
        // 三相电流 - 参考图片数据
        currentA: 0.8 + (Math.random() - 0.5) * 0.2, // 0.8±0.1A
        currentB: 0.7 + (Math.random() - 0.5) * 0.2, // 0.7±0.1A
        currentC: 1.8 + (Math.random() - 0.5) * 0.4, // 1.8±0.2A
        // 两个电功率字段 - 参考图片数据
        power1: 0, // 电功率1
        power2: 0, // 电功率2
        // 状态信息
        communicationStatus: 'normal',
        alarmStatus: 'none',
        // 安全状态
        temperatureStatus: 'normal',
        loadStatus: 'normal',
        insulationStatus: 'good',
      },
    };
  } catch (error) {
    console.error('加载设备信息失败:', error);
  }
};

// 组件挂载时加载数据
onMounted(() => {
  loadDeviceInfo();
});
</script>

<style lang="scss" scoped></style>
