import { defineEventHandler, readBody } from 'h3';
import { useResponseSuccess } from '~/utils/response';
import { enterprises, sites, circuits } from './shared-data';

// 逐日极值数据类型定义
interface PowerExtremeData {
  // 显示字段
  circuitName: string; // 回路名称
  date: number; // 日期（时间戳）
  // 总有功功率(kW)
  totalPowerMaxValue: number; // 最大值数值
  totalPowerMaxTime: number; // 最大值发生时间戳
  totalPowerMinValue: number; // 最小值数值
  totalPowerMinTime: number; // 最小值发生时间戳
  totalPowerAvgValue: number; // 平均值

  // 筛选字段（不在表格中显示，但用于过滤）
  enterpriseId: string; // 企业ID
  energyType: string; // 能源类型
  siteId: string; // 站点ID
  circuitId: string; // 回路ID
  electricalCategory: string; // 电力类别
}

/**
 * 请求参数结构
 */
interface RequestParams {
  query: {
    startTime?: string;
    endTime?: string;
    reportType?: string;
    electricalCategory?: string;
    // 右侧表单的checkbox参数（逐日极值只有一个，0:不勾选, 1:勾选）
    showTotalPower?: number;
    // 左侧筛选表单参数
    enterpriseId?: string;
    energyType?: string;
    siteId?: string;
    keyword?: string;
    circuitNames?: string[];
  };
  pagination: {
    page: number;
    pageSize: number;
  };
}

/**
 * 生成逐日极值Mock数据
 */
function generatePowerExtremeMockData(params: {
  energyType?: string;
  electricalCategory?: string;
}): PowerExtremeData[] {
  const {
    energyType = '1',
    electricalCategory = '1',
  } = params;

  // 使用共用的企业、站点、回路数据
  const data: PowerExtremeData[] = [];

  // 生成从今天往前30天的数据（每天一条记录）
  const dates: Date[] = [];
  const now = new Date();

  for (let dayOffset = 0; dayOffset <= 30; dayOffset++) {
    const date = new Date(now);
    date.setDate(date.getDate() - dayOffset);
    // 设置为当天的00:00:00
    date.setHours(0, 0, 0, 0);
    dates.push(date);
  }

  console.log('🔍 逐日极值数据时间范围（基于当前时间）:', {
    today: now.toISOString().split('T')[0],
    startDate: dates[dates.length - 1].toISOString().split('T')[0],
    endDate: dates[0].toISOString().split('T')[0],
    totalDays: dates.length
  });

  dates.forEach((date, index) => {
    circuits.forEach((circuit, circuitIndex) => {
      const enterprise = enterprises[circuitIndex % enterprises.length];
      const site = sites[circuitIndex % sites.length];

      // 生成当天的极值数据
      const baseValue = 100 + Math.random() * 50; // 基础功率100-150kW

      // 生成最大值（发生在白天）
      const maxTime = new Date(date);
      maxTime.setHours(10 + Math.floor(Math.random() * 8)); // 10:00-18:00
      maxTime.setMinutes(Math.floor(Math.random() * 60));
      maxTime.setSeconds(Math.floor(Math.random() * 60));

      // 生成最小值（发生在夜间）
      const minTime = new Date(date);
      minTime.setHours(Math.floor(Math.random() * 6) + 1); // 01:00-06:00
      minTime.setMinutes(Math.floor(Math.random() * 60));
      minTime.setSeconds(Math.floor(Math.random() * 60));

      const maxValue = Number((baseValue + 20 + Math.random() * 30).toFixed(2)); // 最大值
      const minValue = Number((baseValue - 30 + Math.random() * 20).toFixed(2)); // 最小值
      const avgValue = Number(((maxValue + minValue) / 2 + Math.random() * 10 - 5).toFixed(2)); // 平均值

      data.push({
        circuitName: circuit.name,
        date: date.getTime(), // 返回日期时间戳（00:00:00）
        totalPowerMaxValue: maxValue,
        totalPowerMaxTime: maxTime.getTime(),
        totalPowerMinValue: minValue,
        totalPowerMinTime: minTime.getTime(),
        totalPowerAvgValue: avgValue,

        // 筛选字段
        enterpriseId: enterprise.id,
        energyType: energyType,
        siteId: site.id,
        circuitId: circuit.id,
        electricalCategory: electricalCategory,
      });
    });
  });

  return data;
}

export default defineEventHandler(async (event) => {
  console.log('逐日极值数据API调用 (POST)');
  console.log('🔍 服务器当前时间:', new Date().toISOString());

  // 获取POST请求体
  const requestBody: RequestParams = await readBody(event);

  const { query, pagination } = requestBody;
  const {
    startTime,
    endTime,
    reportType = 'extremeData',
    electricalCategory = '1',
    // 右侧表单的checkbox参数（逐日极值只有一个，0:不勾选, 1:勾选）
    showTotalPower = 1,
    // 左侧筛选表单参数
    enterpriseId = '',
    energyType = '',
    siteId = '',
    keyword = '',
    circuitNames = []
  } = query;

  const { page = 1, pageSize = 50 } = pagination;

  console.log('🔍 查询参数:', {
    startTime,
    endTime,
    enterpriseId,
    siteId,
    electricalCategory,
    checkbox: { showTotalPower }
  });

  // 生成完整的基础数据
  let mockData = generatePowerExtremeMockData({
    energyType: String(energyType),
    electricalCategory: String(electricalCategory),
  });

  console.log('🔍 初始数据量:', mockData.length);

  // 根据时间范围过滤数据
  if (startTime && endTime) {
    const start = new Date(startTime);
    const end = new Date(endTime);
    end.setHours(23, 59, 59, 999);

    const startTimestamp = start.getTime();
    const endTimestamp = end.getTime();

    mockData = mockData.filter(item => {
      return item.date >= startTimestamp && item.date <= endTimestamp;
    });

    console.log('🔍 时间范围筛选后数据量:', mockData.length);
  }

  // 根据筛选条件过滤数据
  if (enterpriseId && enterpriseId.trim()) {
    mockData = mockData.filter(item => item.enterpriseId === enterpriseId);
    console.log('🔍 企业筛选后数据量:', mockData.length);
  }

  if (siteId && siteId.trim()) {
    mockData = mockData.filter(item => item.siteId === siteId);
    console.log('🔍 站点筛选后数据量:', mockData.length);
  }

  if (keyword && keyword.trim()) {
    const searchKeyword = keyword.toLowerCase().trim();
    mockData = mockData.filter(item =>
      item.circuitName.toLowerCase().includes(searchKeyword)
    );
    console.log('🔍 关键字筛选后数据量:', mockData.length);
  }

  if (circuitNames && circuitNames.length > 0) {
    mockData = mockData.filter(item =>
      circuitNames.includes(item.circuitName)
    );
    console.log('🔍 回路筛选后数据量:', mockData.length);
  }

  // 分页处理
  const pageNum = Number(page);
  const pageSizeNum = Number(pageSize);
  const startIndex = (pageNum - 1) * pageSizeNum;
  const endIndex = startIndex + pageSizeNum;
  const paginatedData = mockData.slice(startIndex, endIndex);

  console.log('🔍 分页后数据量:', paginatedData.length);

  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 300));

  return useResponseSuccess({
    items: paginatedData,
    total: mockData.length,
    page: pageNum,
    pageSize: pageSizeNum,
    reportType,
    electricalCategory: String(electricalCategory),
    startTime: String(startTime || ''),
    endTime: String(endTime || ''),
  });
});
