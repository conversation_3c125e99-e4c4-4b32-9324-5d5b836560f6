# MarqueeScroll 跑马灯组件

一个功能丰富的Vue 3跑马灯组件，支持多方向滚动、自定义内容和插槽。

## 特性

- 🎯 支持四个方向滚动（上、下、左、右）
- 🎨 支持插槽自定义内容渲染
- ⏸️ 悬停暂停功能
- 📱 响应式设计，依赖父容器尺寸
- 🎛️ 可配置动画速度和间距
- 🏷️ 内置状态和级别样式
- 🔧 提供控制方法（暂停、恢复、切换）

## 基础用法

```vue
<template>
  <div style="height: 200px; width: 100%;">
    <MarqueeScroll 
      :data-source="marqueeData"
      direction="up"
      :speed="20"
    />
  </div>
</template>

<script setup>
import { MarqueeScroll } from '#/components/ui/marquee';

const marqueeData = [
  {
    id: 1,
    message: '这是一条消息',
    level: 'info',
    status: 'normal'
  },
  {
    id: 2,
    message: '这是一条警告',
    level: 'warning',
    status: 'warning'
  }
];
</script>
```

## 使用插槽自定义内容

```vue
<template>
  <MarqueeScroll :data-source="marqueeData">
    <template #default="{ item, index }">
      <div class="custom-item">
        <span class="index">{{ index + 1 }}.</span>
        <span class="content">{{ item.message }}</span>
        <span class="time">{{ item.time }}</span>
      </div>
    </template>
  </MarqueeScroll>
</template>
```

## Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| dataSource | `MarqueeItem[]` \| `string[]` | `[]` | 数据源 |
| direction | `'up'` \| `'down'` \| `'left'` \| `'right'` | `'up'` | 滚动方向 |
| speed | `number` | `20` | 动画持续时间（秒） |
| gap | `number` | `0.75` | 项目间距（rem） |
| pauseOnHover | `boolean` | `true` | 悬停时是否暂停 |
| wrapperClass | `string` | `''` | 包装器额外类名 |
| height | `string` | `'100%'` | 组件高度 |
| width | `string` | `'100%'` | 组件宽度 |

## MarqueeItem 类型

```typescript
interface MarqueeItem {
  id?: string | number;
  message?: string;
  text?: string;
  level?: 'info' | 'warning' | 'error' | 'success';
  status?: 'normal' | 'warning' | 'error' | 'info';
  [key: string]: any;
}
```

## 方法

通过 ref 可以访问以下方法：

```vue
<template>
  <MarqueeScroll ref="marqueeRef" :data-source="data" />
  <button @click="toggleMarquee">切换动画</button>
</template>

<script setup>
import { ref } from 'vue';

const marqueeRef = ref();

const toggleMarquee = () => {
  marqueeRef.value?.toggle();
};
</script>
```

| 方法 | 说明 |
|------|------|
| `pause()` | 暂停动画 |
| `resume()` | 恢复动画 |
| `toggle()` | 切换动画状态 |

## 样式定制

组件使用CSS变量，可以通过覆盖变量来自定义样式：

```css
.custom-marquee {
  --foreground: #333;
}

.custom-marquee .status-dot {
  width: 8px;
  height: 8px;
}
```

## 注意事项

1. 组件尺寸设置为百分比，需要确保父容器有明确的尺寸
2. 数据源为空时组件不会显示任何内容
3. 使用插槽时，每个数据项都会渲染一次插槽内容
4. 动画基于CSS transform，性能良好
