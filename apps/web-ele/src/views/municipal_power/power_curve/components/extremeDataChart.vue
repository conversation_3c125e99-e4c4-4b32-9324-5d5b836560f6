<script lang="ts" setup>
import type { EchartsUIType } from '@vben/plugins/echarts';
import type { PowerExtremeData } from '../data';

import { computed, nextTick, onMounted, ref, watch } from 'vue';

import { EchartsUI, useEcharts } from '@vben/plugins/echarts';
import dayjs from 'dayjs';

// Props定义
interface ChartDataProps {
  tableData: PowerExtremeData[]; // 表格数据源
  dateRange: [string, string]; // 时间范围
}

interface Props {
  chartData?: ChartDataProps; // 图表数据对象
}

const props = withDefaults(defineProps<Props>(), {
  chartData: () => ({ tableData: [], dateRange: ['', ''] }),
});

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);

// 防抖相关变量
let renderTimer: NodeJS.Timeout | null = null;

// 计算图表标题
const chartTitle = computed(() => {
  if (!props.chartData) return '逐日极值数据';

  const [startTime, endTime] = props.chartData.dateRange;
  if (startTime && endTime) {
    const start = dayjs(startTime).format('YYYY-MM-DD');
    const end = dayjs(endTime).format('YYYY-MM-DD');
    return `${start}~${end} 逐日极值数据`;
  }
  return '逐日极值数据';
});

// 计算图表数据
const processedChartData = computed(() => {
  if (!props.chartData || !props.chartData.tableData || props.chartData.tableData.length === 0) {
    return {
      dateData: [],
      maxValueData: [],
      minValueData: [],
      avgValueData: [],
    };
  }

  // 按日期排序
  const sortedData = [...props.chartData.tableData].sort((a, b) =>
    dayjs(a.date).valueOf() - dayjs(b.date).valueOf()
  );

  // 提取日期轴数据（X轴）
  const dateData = sortedData.map(item =>
    dayjs(item.date).format('MM-DD')
  );

  // 提取极值数据（Y轴）
  const maxValueData = sortedData.map(item => item.totalPowerMaxValue);
  const minValueData = sortedData.map(item => item.totalPowerMinValue);
  const avgValueData = sortedData.map(item => item.totalPowerAvgValue);

  return {
    dateData,
    maxValueData,
    minValueData,
    avgValueData,
  };
});

// 防抖渲染函数
const debouncedRenderChart = () => {
  // 清除之前的定时器
  if (renderTimer) {
    clearTimeout(renderTimer);
  }

  // 设置新的定时器
  renderTimer = setTimeout(() => {
    renderChart();
  }, 50); // 50ms防抖延迟
};

// 渲染图表的函数
const renderChart = () => {
  const { dateData, maxValueData, minValueData, avgValueData } = processedChartData.value;

  if (dateData.length === 0) {
    return;
  }

  renderEcharts({
    title: {
      text: chartTitle.value,
      left: 'center',
      top: '2%',
      textStyle: {
        fontSize: 14,
        fontWeight: 'bold',
      },
    },
    toolbox: {
      feature: {
        saveAsImage: {
          title: '保存为图片',
        },
        dataZoom: {
          title: {
            zoom: '区域缩放',
            back: '区域缩放还原',
          },
        },
        restore: {
          title: '还原',
        },
        dataView: {
          title: '数据视图',
          readOnly: false,
        },
      },
      right: '3%',
      top: '8%',
    },
    grid: {
      bottom: '20%',
      containLabel: true,
      left: '3%',
      right: '8%', // 为右侧dataZoom留出空间
      top: '20%',
    },
    legend: {
      top: '8%',
      left: 'center',
      data: [
        '最大值',
        '最小值',
        '平均值',
      ],
      textStyle: {
        fontSize: 11,
      },
    },
    series: [
      {
        name: '最大值',
        data: maxValueData,
        type: 'line',
        smooth: true,
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(239, 68, 68, 0.3)', // red-500
              },
              {
                offset: 1,
                color: 'rgba(239, 68, 68, 0.05)',
              },
            ],
          },
        },
        lineStyle: {
          color: '#ef4444', // red-500
          width: 2,
        },
        itemStyle: {
          color: '#ef4444',
        },
        symbol: 'circle',
        symbolSize: 4,
      },
      {
        name: '最小值',
        data: minValueData,
        type: 'line',
        smooth: true,
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(16, 185, 129, 0.3)', // emerald-500
              },
              {
                offset: 1,
                color: 'rgba(16, 185, 129, 0.05)',
              },
            ],
          },
        },
        lineStyle: {
          color: '#10b981', // emerald-500
          width: 2,
        },
        itemStyle: {
          color: '#10b981',
        },
        symbol: 'circle',
        symbolSize: 4,
      },
      {
        name: '平均值',
        data: avgValueData,
        type: 'line',
        smooth: true,
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(59, 130, 246, 0.3)', // blue-500
              },
              {
                offset: 1,
                color: 'rgba(59, 130, 246, 0.05)',
              },
            ],
          },
        },
        lineStyle: {
          color: '#3b82f6', // blue-500
          width: 2,
        },
        itemStyle: {
          color: '#3b82f6',
        },
        symbol: 'circle',
        symbolSize: 4,
      },
    ],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        lineStyle: {
          color: '#10b981',
          width: 1,
        },
      },
      formatter(params: any) {
        let result = `${params[0].axisValue}<br/>`;
        params.forEach((param: any) => {
          const value = param.value;
          result += `${param.marker}${param.seriesName}: ${value.toFixed(2)} kW<br/>`;
        });
        return result;
      },
    },
    xAxis: {
      axisTick: {
        show: false,
      },
      boundaryGap: false,
      data: dateData,
      type: 'category',
      axisLine: {
        lineStyle: {
          color: '#e5e7eb',
        },
      },
      axisLabel: {
        fontSize: 11,
        interval: 'auto',
      },
    },
    yAxis: {
      axisTick: {
        show: false,
      },
      type: 'value',
      axisLine: {
        show: false,
      },
      axisLabel: {
        fontSize: 11,
        formatter(value: number) {
          return `${value.toFixed(1)}kW`;
        },
      },
      splitLine: {
        lineStyle: {
          color: '#f3f4f6',
          type: 'dashed',
        },
      },
      axisPointer: {
        lineStyle: {
          color: '#374151',
          width: 1,
        },
      },
    },
    // 添加数据缩放功能
    dataZoom: [
      // 水平方向内部缩放
      {
        type: 'inside',
        orient: 'horizontal',
        start: 0,
        end: 100,
      },
      // 水平方向滑块缩放
      {
        type: 'slider',
        orient: 'horizontal',
        start: 0,
        end: 100,
        height: 20,
        bottom: '8%',
        handleStyle: {
          color: '#3b82f6',
        },
        textStyle: {
          color: '#6b7280',
          fontSize: 10,
        },
      },
      // 垂直方向内部缩放
      {
        type: 'inside',
        orient: 'vertical',
        start: 0,
        end: 100,
      },
      // 垂直方向滑块缩放（右侧）
      {
        type: 'slider',
        orient: 'vertical',
        start: 0,
        end: 100,
        width: 20,
        right: '2%',
        handleStyle: {
          color: '#3b82f6',
        },
        textStyle: {
          color: '#6b7280',
          fontSize: 10,
        },
      },
    ],
  });
};

// 监听图表数据变化，只在数据更新时渲染
watch(() => props.chartData, (newVal, oldVal) => {
  // 只有在数据真正发生变化时才重新渲染
  if (newVal && oldVal && JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
    renderChart(); // 直接渲染，不使用防抖
  } else if (newVal && !oldVal) {
    // 初始化时直接渲染
    renderChart();
  }
}, { deep: true });

onMounted(() => {
  renderChart();
});
</script>

<template>
  <EchartsUI ref="chartRef" height="100%" />
</template>
