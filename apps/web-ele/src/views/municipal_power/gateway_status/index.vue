<script setup lang="ts">
import type { VxeTableGridOptions } from '#/adapter/vxe-table';

import { computed, nextTick, onMounted, ref, watch } from 'vue';

import { IconifyIcon } from '@vben/icons';
import { Page } from '@vben/common-ui';

import { useVbenForm } from '#/adapter/form';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { searchEnterprisesApi } from '#/api/energy/enterprise';
import { getAllSitesApi } from '#/api/power/sites';
import { getGatewayStatusApi } from '#/api/power/gateway-status';

import { useColumns } from './data';

defineOptions({
  name: 'GatewayStatus',
});

// 数据管理
const enterprises = ref<Array<{ label: string; value: string }>>([]);
const sites = ref<Array<{ id: string; name: string; enterpriseId: string }>>([]);
const selectedEnterpriseId = ref('');
const selectedSiteId = ref('');
const loading = ref(false);
const isInitializing = ref(true); // 添加初始化状态标记
const fetchTableDataTimer = ref<number | null>(null); // 防抖定时器
// 移除关键字搜索防抖定时器，统一使用表单的防抖机制

// 表格数据
const tableData = ref<Array<any>>([]);

// 分页状态
const currentPage = ref(1);
const pageSize = ref(20);
const total = ref(0);

// 计算属性
const enterpriseOptions = computed(() => enterprises.value);
const siteOptions = computed(() =>
  sites.value.map(site => ({
    label: site.name,
    value: site.id,
  }))
);

// 统计信息计算属性
const onlineCount = computed(() => {
  const count = tableData.value.filter(item => item.commStatus === 1).length;
  console.log('📊 在线数量计算:', count, '总数据:', tableData.value.length);
  return count;
});

const offlineCount = computed(() => {
  const count = tableData.value.filter(item => item.commStatus === 0).length;
  console.log('📊 离线数量计算:', count);
  return count;
});

const totalCount = computed(() => {
  const count = tableData.value.length;
  console.log('📊 总数量计算:', count);
  return count;
});

const onlineRate = computed(() => {
  if (totalCount.value === 0) return '0%';
  const rate = (onlineCount.value / totalCount.value * 100).toFixed(1);
  console.log('📊 在线率计算:', rate + '%', '在线:', onlineCount.value, '总数:', totalCount.value);
  return `${rate}%`;
});

// 数据获取函数
const loadEnterprises = async () => {
  try {
    loading.value = true;
    enterprises.value = await searchEnterprisesApi();
    console.log('✅ 企业数据获取成功:', enterprises.value);

    // 默认选择第一个企业
    if (enterprises.value.length > 0) {
      selectedEnterpriseId.value = enterprises.value[0].value;
      await loadSitesForEnterprise(selectedEnterpriseId.value);
    }
  } catch (error) {
    console.error('❌ 获取企业数据失败:', error);
    enterprises.value = [];
  } finally {
    loading.value = false;
  }
};

const loadSitesForEnterprise = async (enterpriseId: string) => {
  try {
    loading.value = true;
    sites.value = await getAllSitesApi(enterpriseId);
    console.log('✅ 站点数据获取成功:', sites.value);

    // 默认选择第一个站点
    if (sites.value.length > 0) {
      selectedSiteId.value = sites.value[0].id;

      // 在非初始化期间，站点选择后触发查询
      if (!isInitializing.value) {
        console.log('✅ 站点数据加载完成，触发查询');
        debouncedFetchTableData();
      }
    } else {
      selectedSiteId.value = '';
    }
  } catch (error) {
    console.error('❌ 获取站点数据失败:', error);
    sites.value = [];
    selectedSiteId.value = '';
  } finally {
    loading.value = false;
  }
};

// 处理企业变化
const handleEnterpriseChange = async (enterpriseId: string) => {
  console.log('🔄 企业选择变化:', enterpriseId, '初始化状态:', isInitializing.value);

  // 避免在初始化期间的重复调用
  if (isInitializing.value) {
    console.log('⏭️ 初始化期间，跳过企业变化处理');
    return;
  }

  selectedEnterpriseId.value = enterpriseId;
  selectedSiteId.value = ''; // 清空站点选择

  if (enterpriseId) {
    // 加载站点数据
    await loadSitesForEnterprise(enterpriseId);

    // 站点默认选中第一项（在loadSitesForEnterprise中已经处理）
    // 更新表单值
    searchFormApi.setFieldValue('siteId', selectedSiteId.value);

    // 不在这里调用表格数据API，让站点变化时自动触发
    console.log('✅ 企业变化完成，站点已更新，等待站点变化触发表格刷新');
  } else {
    sites.value = [];
    searchFormApi.setFieldValue('siteId', '');
  }
};

// 处理站点变化
const handleSiteChange = (siteId: string) => {
  console.log('🔄 站点选择变化:', siteId, '初始化状态:', isInitializing.value);

  selectedSiteId.value = siteId;

  // 站点变化时触发查询，重置到第一页
  if (!isInitializing.value && siteId) {
    console.log('✅ 站点变化，重置分页并触发查询');
    currentPage.value = 1; // 重置到第一页
    debouncedFetchTableData();
  } else {
    console.log('⏭️ 初始化期间或站点为空，不触发查询');
  }
};

// 防抖的表格数据获取函数
const debouncedFetchTableData = (delay = 300) => {
  // 清除之前的定时器
  if (fetchTableDataTimer.value) {
    clearTimeout(fetchTableDataTimer.value);
  }

  // 设置新的定时器
  fetchTableDataTimer.value = setTimeout(() => {
    console.log('🚀 防抖后执行表格数据获取');
    fetchTableData();
    fetchTableDataTimer.value = null;
  }, delay);
};

// 分页变化处理函数
const handlePageChange = (params: { currentPage: number; pageSize: number }) => {
  console.log('📄 分页变化:', params);
  currentPage.value = params.currentPage;
  pageSize.value = params.pageSize;
  fetchTableData();
};

// 移除关键字搜索防抖函数，统一使用表单的 submitOnChange 和 submitDebounceTime 机制

const handleSearchSubmit = () => {
  console.log('📝 用户点击提交按钮');

  // 用户主动提交时，直接执行查询
  if (selectedSiteId.value) {
    console.log('✅ 用户提交，重置分页并执行搜索');
    currentPage.value = 1; // 重置到第一页
    fetchTableData(); // 直接调用，不使用防抖
  } else {
    console.log('⚠️ 未选择站点，无法执行搜索');
  }
};

const [SearchForm, searchFormApi] = useVbenForm({
  schema: [
    // 企业选择器
    {
      component: 'SearchSelector',
      fieldName: 'enterpriseId',
      label: '企业',
      componentProps: {
        placeholder: '请选择企业',
        searchPlaceholder: '请输入企业名称搜索',
        buttonType: 'default',
        buttonSize: 'default',
        placement: 'bottom-start',
        popoverWidth: 270,
        filterable: true,
        remote: false,
        loading: loading,
        // 响应式数据绑定
        options: enterpriseOptions,
        // 事件处理 - 企业变化不触发表单提交
        onChange: handleEnterpriseChange,
      },
    },
    // 站点选择器
    {
      component: 'SearchSelector',
      fieldName: 'siteId',
      label: '站点名称',
      componentProps: {
        placeholder: '请先选择企业',
        searchPlaceholder: '请输入站点名称搜索',
        buttonType: 'default',
        buttonSize: 'default',
        placement: 'bottom-start',
        popoverWidth: 270,
        filterable: true,
        remote: false,
        loading: loading,
        disabled: computed(() => !selectedEnterpriseId.value),
        // 响应式数据绑定
        options: siteOptions,
        // 事件处理
        onChange: handleSiteChange,
      },
    },
    {
      component: 'Input',
      componentProps: {
        clearable: true,
        placeholder: '请输入仪表名称',
        // 输入变化时触发查询
        onInput: (value: string) => {
          console.log('🔍 关键字输入变化:', value);
          if (!isInitializing.value && selectedSiteId.value) {
            currentPage.value = 1; // 重置到第一页
            debouncedFetchTableData();
          }
        },
        // 清空时也触发查询
        onClear: () => {
          console.log('🔍 关键字已清空');
          if (!isInitializing.value && selectedSiteId.value) {
            currentPage.value = 1; // 重置到第一页
            debouncedFetchTableData();
          }
        },
      },
      labelWidth: 0,
      fieldName: 'keyword',
      label: '',
    },
    {
      component: 'Checkbox',
      fieldName: 'commLossOnly',
      defaultValue: false, // 默认不勾选
      controlClass: 'text-base md:text-lg',
      labelWidth: 0,
      label: '',
      componentProps: {
        // 复选框变化时触发查询
        onChange: (checked: boolean) => {
          console.log('☑️ 通讯中断复选框变化:', checked);
          if (!isInitializing.value && selectedSiteId.value) {
            currentPage.value = 1; // 重置到第一页
            debouncedFetchTableData();
          }
        },
      },
      renderComponentContent: () => {
        return {
          default: () => ['只显示通讯中断'],
        };
      },
    },
  ],
  submitOnChange: false, // 禁用全局的 submitOnChange，改为单独控制
  showDefaultActions: true,
  submitDebounceTime: 300,
  handleSubmit: handleSearchSubmit,
  // 默认展开搜索表单
  collapsed: false,
  // 所有表单项共用配置
  commonConfig: {
    // 所有表单项统一样式
    componentProps: {
      // class: 'w-full', // 所有表单项的控件样式
    },
    // 控制表单控件的最大宽度，避免在网格布局中过大
    controlClass: 'max-w-xs', // 限制控件最大宽度为 max-w-xs (20rem/320px)
    // labelClass: 'justify-start',
    labelWidth: 80,
    colon: true,
    // formItemClass: 'flex-nowrap',
  },
  // 控制表单是否显示折叠按钮
  showCollapseButton: false,
  // 是否在字段值改变时提交表单
  // 按下回车时是否提交表单
  submitOnEnter: false,
  // 表单布局
  layout: 'horizontal',
  // 使用自定义类名，通过 CSS 覆盖实现 flexbox 布局
  wrapperClass: 'compact-form-layout',
});

// 创建基础表格配置
const createGridOptions = () => {
  return {
    columns: useColumns(),
    height: 'auto',
    keepSource: true,
    showFooter: false,
    // 表格自适应配置
    autoResize: true,
    columnConfig: {
      resizable: true,
      useKey: true,
      isCurrent: true,
    },
    // 列自适应配置
    fit: true, // 列宽自适应
    autoWidth: true, // 自动计算列宽
    // 表格布局配置
    border: true,
    stripe: true,
    showOverflow: true,
    pagerConfig: {
      enabled: true,
      currentPage: 1,
      pageSize: 20,
      pageSizes: [10, 20, 50, 100],
      showTotal: true,
      showJumper: true,
      showSizes: true,
    },
    // 分页事件处理
    onPageChange: handlePageChange,
    rowConfig: {
      keyField: 'meterAddress', // 使用仪表地址作为唯一标识
    },
  } as VxeTableGridOptions<any>;
};

// 创建表格组件
const [GatewayGrid, gatewayGridApi] = useVbenVxeGrid({
  gridOptions: createGridOptions(),
});

// 数据查询函数
const fetchTableData = async () => {
  console.log('📊 fetchTableData 被调用，调用栈:', new Error().stack?.split('\n')[1]);

  // 设置加载状态
  gatewayGridApi.setGridOptions({ loading: true });

  try {
    // 获取表单数据
    const formData = await searchFormApi.getValues();

    // 构建请求参数 - 只传递表单中存在的字段
    const requestParams = {
      query: {
        enterpriseId: formData.enterpriseId || '',
        siteId: formData.siteId || '',
        keyword: formData.keyword || '',
        commLossOnly: formData.commLossOnly || false,
      },
      pagination: {
        page: currentPage.value,
        pageSize: pageSize.value,
      },
    };

    console.log('🚀 发送网关状态API请求:', requestParams);

    const response = await getGatewayStatusApi(requestParams);

    // 设置表格数据
    const responseData = response.items || [];
    tableData.value = responseData; // 保存到响应式变量用于统计计算

    // 更新分页信息
    total.value = response.total || 0;
    currentPage.value = response.page || 1;
    pageSize.value = response.pageSize || 20;

    gatewayGridApi.setGridOptions({
      data: responseData,
      loading: false,
      pagerConfig: {
        enabled: true,
        currentPage: currentPage.value,
        pageSize: pageSize.value,
        total: total.value,
        pageSizes: [10, 20, 50, 100],
        showTotal: true,
        showJumper: true,
        showSizes: true,
      },
    });

    console.log('✅ 网关状态数据加载完成:', responseData.length, '总数:', total.value);
    console.log('📊 统计信息 - 在线:', onlineCount.value, '离线:', offlineCount.value, '在线率:', onlineRate.value);
  } catch (error) {
    console.error('❌ 获取网关状态数据失败:', error);
    // 清空数据
    tableData.value = [];
    // 设置空数据和关闭加载状态
    gatewayGridApi.setGridOptions({
      data: [],
      loading: false,
    });
  }
};

// 页面初始化时加载数据
onMounted(async () => {
  console.log('🚀 网关状态页面初始化...');

  try {
    // 1. 先加载企业数据（会自动选择第一个企业和站点）
    await loadEnterprises();

    // 2. 设置表单默认值
    if (selectedEnterpriseId.value) {
      searchFormApi.setFieldValue('enterpriseId', selectedEnterpriseId.value);
    }
    if (selectedSiteId.value) {
      searchFormApi.setFieldValue('siteId', selectedSiteId.value);
    }

    console.log('✅ 网关状态页面初始化完成');
  } finally {
    // 3. 标记初始化完成，允许后续的响应式更新
    isInitializing.value = false;

    // 4. 初始化完成后，如果站点有数据，默认执行一次查询（输入框为空）
    if (selectedSiteId.value && sites.value.length > 0) {
      console.log('✅ 初始化完成，站点数据已准备好，执行默认查询');
      // 等待一个tick确保表单完全初始化
      await nextTick();
      fetchTableData();
    } else {
      console.log('⚠️ 站点数据未准备好，跳过默认查询');
    }
  }
});

// 注释掉watch监听器，避免重复调用
// 现在通过事件处理器（handleEnterpriseChange, handleSiteChange）来控制表格数据刷新
// watch([selectedEnterpriseId, selectedSiteId], () => {
//   if (!isInitializing.value && selectedEnterpriseId.value && selectedSiteId.value) {
//     console.log('🔄 监听器触发表格刷新');
//     debouncedFetchTableData();
//   }
// }, { immediate: false });
</script>

<template>
  <Page auto-content-height>
    <div class="flex h-full w-full flex-col gap-4">
      <div class="bg-card border-border px-2">
        <SearchForm>
          <template #reset-before>
            <div class="inline-block">
              <div class="flex gap-x-4">
                <div class="flex items-center">
                  <span class="mr-2">在线数量: </span>
                  <span class="text-base text-[#67c23a] md:text-lg">{{ onlineCount }}</span>
                </div>
                <div class="flex items-center">
                  <span class="mr-2">离线数量: </span>
                  <span class="text-base text-[#f56c6c] md:text-lg">{{ offlineCount }}</span>
                </div>
                <div class="flex items-center">
                  <span class="mr-2">在线率: </span>
                  <span
                    class="text-foreground dark:text-foreground text-base md:text-lg"
                    >{{ onlineRate }}</span
                  >
                </div>
              </div>
            </div>
          </template>
        </SearchForm>
      </div>
      <GatewayGrid class="min-h-0 flex-1">
        <!-- 开关状态自定义单元格 -->
        <template #switch-status="{ row }">
          <div class="flex items-center justify-center">
            <IconifyIcon
              :icon="row.switchStatus === 1 ? 'lucide:power' : 'lucide:power-off'"
              :class="row.switchStatus === 1 ? 'text-green-500' : 'text-gray-400'"
              class="h-4 w-4 mr-1"
            />
            <span :class="row.switchStatus === 1 ? 'text-green-600' : 'text-gray-500'">
              {{ row.switchStatus === 1 ? '开启' : '关闭' }}
            </span>
          </div>
        </template>

        <!-- 通讯状态自定义单元格 -->
        <template #comm-status="{ row }">
          <div class="flex items-center justify-center">
            <IconifyIcon
              :icon="row.commStatus === 1 ? 'lucide:wifi' : 'lucide:wifi-off'"
              :class="row.commStatus === 1 ? 'text-green-500' : 'text-red-500'"
              class="h-4 w-4 mr-1"
            />
            <span :class="row.commStatus === 1 ? 'text-green-600' : 'text-red-600'">
              {{ row.commStatus === 1 ? '在线' : '离线' }}
            </span>
          </div>
        </template>
      </GatewayGrid>
    </div>
  </Page>
</template>

<style scoped lang="scss">
// 响应式设计
@media (max-width: 768px) {
  :deep(.vxe-table) {
    font-size: 12px;
  }
}
:deep(.vxe-pager.size--mini) {
  margin-top: 2px;
}

// 强制覆盖 Vben Form 的硬编码 grid 布局
:deep(.compact-form-layout) {
  // 使用 !important 强制覆盖硬编码的 grid 类
  display: flex !important;
  flex-wrap: wrap !important;
  align-items: center !important;
  gap: 1rem 2.5rem !important;
  justify-content: flex-start !important;

  // 大屏幕不换行，表单项水平排列
  @media (min-width: 1200px) {
    flex-wrap: nowrap !important;
    gap: 1rem 3rem !important;
  }

  // 中等屏幕适当换行
  @media (max-width: 1199px) and (min-width: 768px) {
    gap: 1rem 2rem !important;
  }

  // 小屏幕垂直排列
  @media (max-width: 767px) {
    flex-direction: column !important;
    align-items: stretch !important;
    gap: 1rem !important;
  }

  // 表单项容器样式
  > * {
    flex: 0 0 auto !important;
    margin-bottom: 0 !important;
    padding: 12px 0 !important;
    // 小屏幕下占满宽度
    @media (max-width: 767px) {
      flex: 1 1 100% !important;
      width: 100% !important;
    }
  }

  // 操作按钮区域特殊处理 - 紧贴表单项，空间不够时自动换行
  > *:last-child {
    flex: 0 1 auto !important; // 允许收缩，空间不够时换行
    width: auto !important; // 覆盖 w-full 的 width: 100%
    min-width: fit-content !important; // 最小宽度为内容宽度

    // 强制覆盖组件内联样式 - 使用更高优先级
    grid-column: unset !important; // 覆盖 col-span-full 和内联样式
    margin-left: 0 !important; // 覆盖内联样式的 margin-left: auto

    // 针对内部的 form-actions 组件
    &.w-full {
      width: auto !important; // 覆盖组件自身的 w-full
    }

    &.col-span-full {
      grid-column: unset !important; // 覆盖 col-span-full 类
    }

    &.text-right {
      text-align: left !important; // 覆盖右对齐
    }

    .w-full {
      width: auto !important; // 覆盖组件内部的 w-full
    }

    @media (max-width: 767px) {
      flex: 1 1 100% !important; // 小屏幕下独占一行
      width: 100% !important; // 小屏幕下恢复全宽
      grid-column: 1 / -1 !important; // 小屏幕下恢复全列
      margin-left: auto !important; // 小屏幕下恢复右对齐

      &.w-full {
        width: 100% !important;
      }

      &.text-right {
        text-align: right !important; // 小屏幕下恢复右对齐
      }

      .w-full {
        width: 100% !important;
      }
    }
  }

  // 确保表单控件保持合适的宽度
  .el-select,
  .el-date-editor,
  .el-input,
  .el-cascader {
    min-width: 180px !important; // 设置最小宽度
    width: auto !important;
  }

  // 日期范围选择器需要更大的宽度
  .el-date-editor--daterange,
  .el-date-editor--datetimerange {
    min-width: 280px !important;
  }

  // 下拉选择器特殊处理
  .el-select {
    .el-input {
      min-width: 180px !important;
    }
  }
}

// 更强的选择器，确保覆盖硬编码的 grid 类
:deep(div.compact-form-layout.grid) {
  display: flex !important;
  flex-wrap: wrap !important;
  align-items: center !important;
  gap: 1rem 2.5rem !important;
  justify-content: flex-start !important;
}
</style>
