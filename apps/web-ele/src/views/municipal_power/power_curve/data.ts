import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';

import dayjs from 'dayjs';

// 报表类型定义
export type ReportType = 'rawData' | 'extremeData';

// 日原始数据类型定义
export interface PowerCurveData {
  circuitName: string; // 回路名称
  date: number; // 采集时间（时间戳）
  pa: number; // Pa(kW) - A相功率
  pb: number; // Pb(kW) - B相功率
  pc: number; // Pc(kW) - C相功率
  p: number; // P(kW) - 总功率
}

// 逐日极值数据类型定义
export interface PowerExtremeData {
  circuitName: string; // 回路名称
  date: number; // 日期（时间戳）
  // 总有功功率(kW)
  totalPowerMaxValue: number; // 最大值数值
  totalPowerMaxTime: number; // 最大值发生时间戳
  totalPowerMinValue: number; // 最小值数值
  totalPowerMinTime: number; // 最小值发生时间戳
  totalPowerAvgValue: number; // 平均值
}

/**
 * 日原始数据表格列配置函数
 */
export function useRawDataColumns(
  onActionClick?: OnActionClickFn<PowerCurveData>,
): VxeTableGridOptions<PowerCurveData>['columns'] {
  const columns: VxeTableGridOptions<PowerCurveData>['columns'] = [
    // 回路名称列
    {
      field: 'circuitName',
      title: '回路名称',
      width: 120,
      fixed: 'left',
      align: 'center',
      headerAlign: 'center',
    },
    // 采集时间列（支持排序）
    {
      field: 'date',
      title: '采集时间',
      width: 160,
      fixed: 'left',
      align: 'center',
      headerAlign: 'center',
      sortable: true, // 支持排序
      formatter: ({ cellValue }) => {
        if (!cellValue) return '';
        return dayjs(cellValue).format('YYYY-MM-DD HH:mm');
      },
    },
  ];

  // 添加功率相关列
  columns.push(
    // Pa(kW) - A相功率
    {
      field: 'pa',
      title: 'Pa(kW)',
      minWidth: 100,
      align: 'right',
      headerAlign: 'center',
      formatter: ({ cellValue }) => cellValue?.toFixed(2) || '0.00',
    },
    // Pb(kW) - B相功率
    {
      field: 'pb',
      title: 'Pb(kW)',
      minWidth: 100,
      align: 'right',
      headerAlign: 'center',
      formatter: ({ cellValue }) => cellValue?.toFixed(2) || '0.00',
    },
    // Pc(kW) - C相功率
    {
      field: 'pc',
      title: 'Pc(kW)',
      minWidth: 100,
      align: 'right',
      headerAlign: 'center',
      formatter: ({ cellValue }) => cellValue?.toFixed(2) || '0.00',
    },
    // P(kW) - 总功率
    {
      field: 'p',
      title: 'P(kW)',
      minWidth: 100,
      align: 'right',
      headerAlign: 'center',
      formatter: ({ cellValue }) => cellValue?.toFixed(2) || '0.00',
    },
  );

  return columns;
}

/**
 * 逐日极值数据表格列配置函数
 */
export function useExtremeDataColumns(
  onActionClick?: OnActionClickFn<PowerExtremeData>,
): VxeTableGridOptions<PowerExtremeData>['columns'] {
  const columns: VxeTableGridOptions<PowerExtremeData>['columns'] = [
    // 回路名称列 - 跨3行
    {
      field: 'circuitName',
      title: '回路名称',
      minWidth: 120,
      fixed: 'left',
      align: 'center',
      headerAlign: 'center',
    },
    // 日期列 - 跨3行
    {
      field: 'date',
      title: '日期',
      minWidth: 120,
      fixed: 'left',
      align: 'center',
      headerAlign: 'center',
      sortable: true,
      formatter: ({ cellValue }) => {
        if (!cellValue) return '';
        return dayjs(cellValue).format('YYYY-MM-DD');
      },
    },
    // 总有功功率(kW) - 主列
    {
      title: '总有功功率(kW)',
      align: 'center',
      headerAlign: 'center',
      children: [
        // 最大值 - 子列
        {
          title: '最大值',
          align: 'center',
          headerAlign: 'center',
          children: [
            {
              field: 'totalPowerMaxValue',
              title: '数值',
              minWidth: 80,
              align: 'right',
              headerAlign: 'center',
              formatter: ({ cellValue }) => cellValue?.toFixed(2) || '0.00',
            },
            {
              field: 'totalPowerMaxTime',
              title: '发生时间',
              minWidth: 120,
              align: 'center',
              headerAlign: 'center',
              formatter: ({ cellValue }) => {
                if (!cellValue) return '';
                return dayjs(cellValue).format('HH:mm:ss');
              },
            },
          ],
        },
        // 最小值 - 子列
        {
          title: '最小值',
          align: 'center',
          headerAlign: 'center',
          children: [
            {
              field: 'totalPowerMinValue',
              title: '数值',
              minWidth: 80,
              align: 'right',
              headerAlign: 'center',
              formatter: ({ cellValue }) => cellValue?.toFixed(2) || '0.00',
            },
            {
              field: 'totalPowerMinTime',
              title: '发生时间',
              minWidth: 120,
              align: 'center',
              headerAlign: 'center',
              formatter: ({ cellValue }) => {
                if (!cellValue) return '';
                return dayjs(cellValue).format('HH:mm:ss');
              },
            },
          ],
        },
        // 平均值 - 子列（跨2行）
        {
          field: 'totalPowerAvgValue',
          title: '平均值',
          minWidth: 80,
          align: 'right',
          headerAlign: 'center',
          formatter: ({ cellValue }) => cellValue?.toFixed(2) || '0.00',
        },
      ],
    },
  ];

  return columns;
}

/**
 * 根据报表类型获取对应的表格列配置
 */
export function useColumns(
  reportType: ReportType = 'rawData',
  onActionClick?: OnActionClickFn<any>,
) {
  if (reportType === 'extremeData') {
    return useExtremeDataColumns(onActionClick);
  }
  return useRawDataColumns(onActionClick);
}
