<script lang="ts" setup>
import { onMounted, ref } from 'vue';

import {
  EchartsUI,
  type EchartsUIType,
  useEcharts,
} from '@vben/plugins/echarts';

const chartRef = ref<EchartsUIType>();
const { renderEcharts } = useEcharts(chartRef);

// 生成24小时的时间标签
const generateTimeLabels = () => {
  const labels = [];
  for (let i = 0; i < 24; i++) {
    labels.push(`${i.toString().padStart(2, '0')}:00`);
  }
  return labels;
};

// 生成模拟的三相电流数据 - 参考图片中的波动特征
const generateCurrentData = (baseValue: number, variance: number) => {
  const data = [];
  for (let i = 0; i < 24; i++) {
    // 模拟一天中的电流变化，白天用电量较高，夜间较低
    const timeMultiplier = Math.sin((i / 24) * Math.PI * 2 + Math.PI / 2) * 0.3 + 0.7;
    // 添加随机波动
    const randomVariance = (Math.random() - 0.5) * variance;
    const value = baseValue * timeMultiplier + randomVariance;
    data.push(Math.max(0, Number(value.toFixed(2))));
  }
  return data;
};

onMounted(() => {
  const timeLabels = generateTimeLabels();
  
  renderEcharts({
    toolbox: {
      feature: {
        saveAsImage: {
          title: '保存为图片',
        },
        dataZoom: {
          title: {
            zoom: '区域缩放',
            back: '区域缩放还原',
          },
        },
        restore: {
          title: '还原',
        },
        dataView: {
          title: '数据视图',
          readOnly: false,
        },
      },
      right: '3%',
      top: '8%',
    },
    grid: {
      bottom: '15%',
      containLabel: true,
      left: '3%',
      right: '3%',
      top: '20%',
    },
    legend: {
      bottom: '3%',
      data: ['A相电流', 'B相电流', 'C相电流'],
      textStyle: {
        fontSize: 11,
      },
    },
    series: [
      {
        name: 'A相电流',
        data: generateCurrentData(0.8, 0.3), // 基准0.8A，波动±0.15A
        type: 'line',
        smooth: true,
        lineStyle: {
          color: '#ff6b35', // 橙红色 - 参考图片
          width: 2,
        },
        itemStyle: {
          color: '#ff6b35',
        },
        symbol: 'circle',
        symbolSize: 3,
      },
      {
        name: 'B相电流',
        data: generateCurrentData(0.7, 0.25), // 基准0.7A，波动±0.125A
        type: 'line',
        smooth: true,
        lineStyle: {
          color: '#4ecdc4', // 青色 - 参考图片
          width: 2,
        },
        itemStyle: {
          color: '#4ecdc4',
        },
        symbol: 'circle',
        symbolSize: 3,
      },
      {
        name: 'C相电流',
        data: generateCurrentData(1.8, 0.4), // 基准1.8A，波动±0.2A
        type: 'line',
        smooth: true,
        lineStyle: {
          color: '#45b7d1', // 蓝色 - 参考图片
          width: 2,
        },
        itemStyle: {
          color: '#45b7d1',
        },
        symbol: 'circle',
        symbolSize: 3,
      },
    ],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        lineStyle: {
          color: '#666',
          width: 1,
          type: 'dashed',
        },
      },
      formatter: function (params: any) {
        let result = `时间: ${params[0].axisValue}<br/>`;
        params.forEach((param: any) => {
          result += `${param.marker}${param.seriesName}: ${param.value} A<br/>`;
        });
        return result;
      },
    },
    xAxis: {
      axisTick: {
        show: false,
      },
      boundaryGap: false,
      data: timeLabels,
      type: 'category',
      axisLabel: {
        fontSize: 11,
        interval: 2, // 每隔2个小时显示一个标签
      },
    },
    yAxis: {
      axisTick: {
        show: false,
      },
      type: 'value',
      axisLine: {
        show: false,
      },
      axisLabel: {
        fontSize: 11,
        formatter: '{value} A',
      },
      splitLine: {
        lineStyle: {
          color: '#f3f4f6',
          type: 'dashed',
        },
      },
      min: 0,
      max: 3, // 根据数据范围设置最大值
    },
  });
});
</script>

<template>
  <EchartsUI ref="chartRef" height="100%" />
</template>
