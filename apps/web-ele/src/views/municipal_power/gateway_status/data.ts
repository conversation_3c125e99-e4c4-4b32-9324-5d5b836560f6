import type { OnActionClickFn, VxeTableGridOptions } from '#/adapter/vxe-table';

// 网关状态数据类型定义
export interface GatewayStatusData {
  stationName: string; // 变配电站名称
  gateway: string; // 网关
  serialPort: string; // 串口号
  meterAddress: string; // 仪表地址
  meterName: string; // 仪表名称
  switchStatus: number; // 开关状态：1-开启，0-关闭
  commStatus: number; // 通讯状态：1-在线，0-离线
  offlineTime: number; // 离线时间（时间戳）
  lastCollectTime: number; // 最后采集时间（时间戳）
  totalInterruptTime: number; // 累计中断时间（时间戳）
}

/**
 * 网关状态表格列配置函数
 *
 * 该函数用于生成网关状态表格的列配置，包含以下列：
 * - 变配电站名称、间次、开口号、仪表地址、仪表名称、开关状态、通讯状态、离线时间、最后采集时间、累计中断时间
 *
 * @param onActionClick - 可选的操作按钮点击回调函数
 *
 * @returns VxeTableGridOptions<GatewayStatusData>['columns']
 *   返回 VXE Table 表格的列配置数组
 */
export function useColumns(
  onActionClick?: OnActionClickFn<GatewayStatusData>,
): VxeTableGridOptions<GatewayStatusData>['columns'] {
  console.log('Creating gateway status columns');

  const columns: VxeTableGridOptions<GatewayStatusData>['columns'] = [
    // 变配电站名称
    {
      field: 'stationName',
      title: '变配电站名称',
      minWidth: 140,
      fixed: 'left',
      align: 'center',
      headerAlign: 'center',
    },
    // 网关
    {
      field: 'gateway',
      title: '网关',
      minWidth: 80,
      align: 'center',
      headerAlign: 'center',
    },
    // 串口号
    {
      field: 'serialPort',
      title: '串口号',
      minWidth: 80,
      align: 'center',
      headerAlign: 'center',
    },
    // 仪表地址
    {
      field: 'meterAddress',
      title: '仪表地址',
      minWidth: 100,
      align: 'center',
      headerAlign: 'center',
    },
    // 仪表名称
    {
      field: 'meterName',
      title: '仪表名称',
      minWidth: 120,
      align: 'center',
      headerAlign: 'center',
    },
    // 开关状态
    {
      field: 'switchStatus',
      title: '开关状态',
      minWidth: 100,
      align: 'center',
      headerAlign: 'center',
      slots: { default: 'switch-status' }, // 使用自定义插槽
    },
    // 通讯状态
    {
      field: 'commStatus',
      title: '通讯状态',
      minWidth: 100,
      align: 'center',
      headerAlign: 'center',
      slots: { default: 'comm-status' }, // 使用自定义插槽
    },
    // 离线时间
    {
      field: 'offlineTime',
      title: '离线时间',
      minWidth: 160,
      align: 'center',
      headerAlign: 'center',
      sortable: true,
      formatter: ({ cellValue }) => {
        if (!cellValue || cellValue === 0) return '-';
        return new Date(cellValue).toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
        });
      },
    },
    // 最后采集时间
    {
      field: 'lastCollectTime',
      title: '最后采集时间',
      minWidth: 160,
      align: 'center',
      headerAlign: 'center',
      sortable: true,
      formatter: ({ cellValue }) => {
        if (!cellValue) return '-';
        return new Date(cellValue).toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
        });
      },
    },
    // 累计中断时间
    {
      field: 'totalInterruptTime',
      title: '累计中断时间',
      minWidth: 140,
      align: 'center',
      headerAlign: 'center',
      sortable: true,
      formatter: ({ cellValue }) => {
        if (!cellValue || cellValue === 0) return '0小时';
        // 将时间戳转换为小时数显示
        const hours = Math.floor(cellValue / (1000 * 60 * 60));
        const minutes = Math.floor(
          (cellValue % (1000 * 60 * 60)) / (1000 * 60),
        );
        return `${hours}小时${minutes}分钟`;
      },
    },
  ];

  return columns;
}
