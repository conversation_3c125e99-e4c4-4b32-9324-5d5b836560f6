<script setup lang="ts">
import type { SiteInfo } from '#/components/services/site-selector/types';

import { ref, onMounted } from 'vue';

import { AnalysisChartCard, Page } from '@vben/common-ui';

import { ElOption, ElSelect } from 'element-plus';

import SiteSelector from '#/components/services/site-selector/index.vue';

import RealTimeElectricityChart from './components/RealTimeElectricityChart.vue';
import RealTimeLoadChart from './components/RealTimeLoadChart.vue';
import WiringDiagram from './components/WiringDiagram.vue';

defineOptions({
  name: 'WiringDiagram',
});

// 站点选择器相关
const selectedSite = ref('微电网研究院总部'); // 设置默认值为第一项
const currentSiteInfo = ref<SiteInfo | null>(null);

// 站点选择事件处理
function handleSiteSelected(site: SiteInfo) {
  console.log('选中的站点信息:', site);
  currentSiteInfo.value = site;

  // 这里可以根据选中的站点信息更新其他数据
  // 比如更新图表数据、刷新统计信息等
}

// 组件挂载时设置默认站点信息
onMounted(() => {
  // 设置默认站点信息（第一项）
  currentSiteInfo.value = {
    id: 'site_1',
    name: '微电网研究院总部',
    enterprise: '微电网研究院',
    region: '无锡市',
    address: '无锡市江阴市江苏科技城创新大道1号',
    type: 'substation',
  };
});

const value = ref('1');

const options = [
  {
    value: '1',
    label: '受电柜',
  },
];


</script>
<template>
  <Page auto-content-height>
    <div class="flex h-full gap-4 overflow-hidden">
      <div
        class="border-border flex h-full w-1/5 min-w-[400px] shrink-0 flex-col gap-4"
      >
        <AnalysisChartCard title="配电信息" class="flex-1">
          <div class="flex flex-col gap-4">
            <div class="flex flex-col gap-2">
              <span class="text-foreground whitespace-nowrap">站点名称:</span>
              <SiteSelector
                v-model="selectedSite"
                placeholder="请选择站点"
                class="w-48"
                @site-selected="handleSiteSelected"
              />
            </div>
            <div class="flex justify-between">
              <span class="text-muted-foreground">地址:</span>
              <span class="text-foreground">{{
                currentSiteInfo?.address || '无锡市江阴市江苏科技城创新大道1号'
              }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-muted-foreground">变电电压等级:</span>
              <span class="text-foreground">10.0kV</span>
            </div>
            <div class="flex justify-between">
              <span class="text-muted-foreground">变压器容量:</span>
              <span class="text-foreground">500kVA</span>
            </div>
            <div class="flex justify-between">
              <span class="text-muted-foreground">最大需量:</span>
              <span class="text-foreground">kW</span>
            </div>
            <div class="flex justify-between">
              <span class="text-muted-foreground">变压器数量:</span>
              <span class="text-foreground">1台</span>
            </div>
          </div>
        </AnalysisChartCard>
        <AnalysisChartCard title="实时负荷" class="flex-1">
          <template #title-actions>
            <div class="w-32">
              <ElSelect v-model="value" placeholder="">
                <ElOption
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </ElSelect>
            </div>
          </template>
          <RealTimeLoadChart />
        </AnalysisChartCard>
        <AnalysisChartCard title="实时电量" class="flex-1">
          <RealTimeElectricityChart />
        </AnalysisChartCard>
      </div>
      <div class="bg-card border-border h-full min-w-max flex-1 rounded-lg">
        <WiringDiagram />
      </div>
    </div>
  </Page>
</template>
