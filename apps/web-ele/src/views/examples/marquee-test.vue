<template>
  <div class="p-8">
    <h1 class="text-2xl font-bold mb-4">跑马灯测试</h1>
    
    <!-- 测试容器 -->
    <div class="border border-gray-300 rounded-lg p-4 mb-4">
      <h2 class="text-lg font-semibold mb-2">基础测试</h2>
      <div style="height: 200px; width: 100%; background: #f0f0f0; border: 1px solid #ccc;">
        <MarqueeScroll 
          :data-source="testData"
          direction="up"
          :speed="10"
          :pause-on-hover="true"
        />
      </div>
    </div>

    <!-- 数据显示 -->
    <div class="border border-gray-300 rounded-lg p-4">
      <h2 class="text-lg font-semibold mb-2">数据源</h2>
      <pre>{{ JSON.stringify(testData, null, 2) }}</pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import MarqueeScroll from '#/components/ui/marquee/MarqueeScroll.vue';
import type { MarqueeItem } from '#/components/ui/marquee/MarqueeScroll.vue';

defineOptions({
  name: 'MarqueeTest',
});

const testData: MarqueeItem[] = [
  {
    id: 1,
    message: '这是第一条测试消息',
    level: 'info',
    status: 'info',
  },
  {
    id: 2,
    message: '这是第二条测试消息',
    level: 'warning',
    status: 'warning',
  },
  {
    id: 3,
    message: '这是第三条测试消息',
    level: 'error',
    status: 'error',
  },
];
</script>
