import { defineEventHandler, readBody } from 'h3';
import { useResponseSuccess } from '~/utils/response';
import { enterprises, sites, circuits } from './shared-data';

// 电力曲线报表数据类型定义
interface PowerCurveData {
  // 显示字段
  circuitName: string; // 回路名称
  date: number; // 采集时间（时间戳）
  pa: number; // Pa(kW) - A相功率
  pb: number; // Pb(kW) - B相功率
  pc: number; // Pc(kW) - C相功率
  p: number; // P(kW) - 总功率

  // 筛选字段（不在表格中显示，但用于过滤）
  enterpriseId: string; // 企业ID
  energyType: string; // 能源类型
  siteId: string; // 站点ID
  circuitId: string; // 回路ID
  timeInterval: string; // 时间间隔
  electricalCategory: string; // 电力类别
}

/**
 * 请求参数结构
 */
interface RequestParams {
  query: {
    startTime?: string;
    endTime?: string;
    reportType?: string;
    timeInterval?: string;
    electricalCategory?: string;
    // 右侧表单的checkbox参数（0:不勾选, 1:勾选）
    showPhaseA?: number;
    showPhaseB?: number;
    showPhaseC?: number;
    showTotalPower?: number;
    // 左侧筛选表单参数
    enterpriseId?: string;
    energyType?: string;
    siteId?: string;
    keyword?: string;
    circuitNames?: string[];
  };
  pagination: {
    page: number;
    pageSize: number;
  };
}

/**
 * 生成电力曲线Mock数据
 */
function generatePowerCurveMockData(params: {
  timeInterval?: string;
  energyType?: string;
  electricalCategory?: string;
}): PowerCurveData[] {
  const {
    timeInterval = '1',
    energyType = '1',
    electricalCategory = '1',
  } = params;

  // 使用共用的企业、站点、回路数据
  const data: PowerCurveData[] = [];

  // 生成完整的基础数据（基于当前用户时间及往前30天）
  const dates: Date[] = [];
  const now = new Date();

  // 生成从今天往前30天的数据（包含今天，共31天）
  for (let dayOffset = 0; dayOffset <= 30; dayOffset++) {
    const date = new Date(now);
    date.setDate(date.getDate() - dayOffset);
    dates.push(date);
  }

  console.log('🔍 电力曲线数据时间范围（基于当前时间）:', {
    today: now.toISOString().split('T')[0],
    startDate: dates[dates.length - 1].toISOString().split('T')[0], // 最早日期
    endDate: dates[0].toISOString().split('T')[0], // 最晚日期（今天）
    totalDays: dates.length
  });

  dates.forEach((date, index) => {
    // 每天生成多条记录（根据日期哈希决定数量，保持一致性）
    const dateString = date.toISOString().split('T')[0];
    const dateHash = dateString.split('').reduce((hash, char) => hash + char.charCodeAt(0), 0);
    const recordsPerDay = (dateHash % 10) + 5; // 每天5-14条记录

    for (let recordIndex = 0; recordIndex < recordsPerDay; recordIndex++) {
      const enterprise = enterprises[recordIndex % enterprises.length];
      const site = sites[recordIndex % sites.length];
      const circuit = circuits[recordIndex % circuits.length];

      // 生成当天的随机时间
      const collectTime = new Date(date);
      collectTime.setHours(Math.floor(Math.random() * 24));
      collectTime.setMinutes(Math.floor(Math.random() * 60));
      collectTime.setSeconds(Math.floor(Math.random() * 60));

      // 生成基础功率数据
      const basePower = 8 + Math.random() * 5; // 8-13kW 基础功率

      // 生成三相功率数据（Pa, Pb, Pc）
      const pa = Number((basePower + Math.random() * 2 - 1).toFixed(2)); // A相功率
      const pb = Number((basePower + Math.random() * 2 - 1).toFixed(2)); // B相功率
      const pc = Number((basePower + Math.random() * 2 - 1).toFixed(2)); // C相功率
      const p = Number((pa + pb + pc).toFixed(2)); // 总功率

      data.push({
        circuitName: circuit.name,
        date: collectTime.getTime(), // 返回时间戳
        pa,
        pb,
        pc,
        p,

        // 筛选字段
        enterpriseId: enterprise.id,
        energyType: energyType,
        siteId: site.id,
        circuitId: circuit.id,
        timeInterval: timeInterval,
        electricalCategory: electricalCategory,
      });
    }
  });

  return data;
}

export default defineEventHandler(async (event) => {
  console.log('电力曲线报表API调用 (POST)');
  console.log('🔍 服务器当前时间:', new Date().toISOString());

  // 获取POST请求体
  const requestBody: RequestParams = await readBody(event);

  const { query, pagination } = requestBody;
  const {
    startTime,
    endTime,
    reportType = 'rawData',
    timeInterval = '1',
    electricalCategory = '1',
    // 右侧表单的checkbox参数（0:不勾选, 1:勾选）
    showPhaseA = 1,
    showPhaseB = 1,
    showPhaseC = 1,
    showTotalPower = 1,
    // 左侧筛选表单参数
    enterpriseId = '',
    energyType = '',
    siteId = '',
    keyword = '',
    circuitNames = []
  } = query;

  const { page = 1, pageSize = 50 } = pagination;

  console.log('🔍 查询参数:', {
    startTime,
    endTime,
    enterpriseId,
    siteId,
    timeInterval,
    electricalCategory,
    checkbox: { showPhaseA, showPhaseB, showPhaseC, showTotalPower }
  });

  // 生成完整的基础数据（基于当前时间及往前30天）
  let mockData = generatePowerCurveMockData({
    timeInterval: String(timeInterval),
    energyType: String(energyType),
    electricalCategory: String(electricalCategory),
  });

  console.log('🔍 初始数据量:', mockData.length);

  // 根据时间范围过滤数据
  if (startTime && endTime) {
    const start = new Date(startTime);
    const end = new Date(endTime);
    // 设置结束时间为当天的23:59:59
    end.setHours(23, 59, 59, 999);

    const startTimestamp = start.getTime();
    const endTimestamp = end.getTime();

    mockData = mockData.filter(item => {
      return item.date >= startTimestamp && item.date <= endTimestamp;
    });

    console.log('🔍 时间范围筛选后数据量:', mockData.length);
  }

  // 根据筛选条件过滤数据
  if (enterpriseId && enterpriseId.trim()) {
    mockData = mockData.filter(item => item.enterpriseId === enterpriseId);
    console.log('🔍 企业筛选后数据量:', mockData.length);
  }

  if (siteId && siteId.trim()) {
    mockData = mockData.filter(item => item.siteId === siteId);
    console.log('🔍 站点筛选后数据量:', mockData.length);
  }

  if (keyword && keyword.trim()) {
    const searchKeyword = keyword.toLowerCase().trim();
    mockData = mockData.filter(item =>
      item.circuitName.toLowerCase().includes(searchKeyword)
    );
    console.log('🔍 关键字筛选后数据量:', mockData.length);
  }

  if (circuitNames && circuitNames.length > 0) {
    mockData = mockData.filter(item =>
      circuitNames.includes(item.circuitName)
    );
    console.log('🔍 回路筛选后数据量:', mockData.length);
  }

  // 分页处理
  const pageNum = Number(page);
  const pageSizeNum = Number(pageSize);
  const startIndex = (pageNum - 1) * pageSizeNum;
  const endIndex = startIndex + pageSizeNum;
  const paginatedData = mockData.slice(startIndex, endIndex);

  console.log('🔍 分页后数据量:', paginatedData.length);

  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 300));

  return useResponseSuccess({
    items: paginatedData,
    total: mockData.length,
    page: pageNum,
    pageSize: pageSizeNum,
    reportType,
    timeInterval: String(timeInterval),
    startTime: String(startTime || ''),
    endTime: String(endTime || ''),
  });
});
